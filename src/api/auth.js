import http from '../services/common/httpService';
import SharedCache from "../sharedCache";
import config from '../config.json';

// Helper to set userType in SharedCache and localStorage
function setUserTypeGlobally(type) {
    SharedCache.set("userType", type);
    localStorage.setItem("userType", type);
}

// Helper to get userType
export function getUserType() {
    return SharedCache.get("userType") || localStorage.getItem("userType") || 'msme'; // Default to 'msme'
}

// New: Helper to get the full user object from cache
export function getCurrentUser() {
    return SharedCache.get("user");
}

// New: Function to fetch buyer data from backend (if different from generic user profile)
export async function fetchBuyerData(email) {
    try {
        const response = await fetch(`${config.apiUrl}/ops/invoiceFinancing/buyer-admins/email?email=${encodeURIComponent(email)}`, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                // You might need an auth token here if this endpoint is protected
                'x-auth-token': SharedCache.get("token") // Assuming buyer data fetch also needs token
            },
        });
        const data = await response.json();
        if (response.ok && data.success) {
            return data.buyer; // Assuming backend returns buyer data under 'buyer' key
        } else {
            console.error("Failed to fetch buyer data:", data.message || "Unknown error");
            return null;
        }
    } catch (error) {
        console.error("Error fetching buyer data:", error);
        return null;
    }
}


/**
 * Function to request OTP for login
 * @param {string} phone - Phone number
 * @param {boolean} isSignUp - Flag indicating if it's a sign-up flow
 * @param {string} desiredUserType - The user type ('msme' or 'buyer') chosen during signup
 * @returns {Promise<Object>} Response data
 */
export async function fetchOtpForLogin(phoneWithDialCode, isSignUp = false, desiredUserType = 'msme') {
    console.log("Requesting OTP for:", phoneWithDialCode, "isSignUp:", isSignUp, "desiredUserType:", desiredUserType);

    try {
        const response = await fetch(config.apiUrl + `/ops/invoiceFinancing/send-otp`, {
            method: "POST",
            headers: {
                "Content-Type": "application/json",
            },
            body: JSON.stringify({
                mobile: phoneWithDialCode,
                isSignUp: isSignUp,
                userType: desiredUserType // Pass user type during OTP request for signup
            }),
        });

        const data = await response.json();

        if (!response.ok || !data.success) {
            return {
                success: false,
                message: data.message || "Failed to send OTP",
                authMethod: data.authMethod || null,
                email: data.email || null
            };
        }

        return { success: true };
    } catch (error) {
        console.error("Actual OTP failed, using fallback:", error.message);
        return {
            success: false,
            fallback: true,
            message: error.message || "OTP send failed. Falling back to fake OTP.",
        };
    }
}

// Placeholder for sending OTP to phone
export const sendOtpPhone = async (fullPhoneNumber) => {
    console.log("API: Sending OTP to phone", fullPhoneNumber);
    const response = await http.post(`${config.apiUrl}/ops/invoiceFinancing/send-otp`, { mobile: fullPhoneNumber });
    return response.data;
};

// Placeholder for verifying phone OTP
export const verifyOtpPhone = async (fullPhoneNumber, otp) => {
    console.log("Mock API: Verifying phone OTP", fullPhoneNumber, otp);
    const response = await http.post(`${config.apiUrl}/ops/invoiceFinancing/verify-otp`, { mobile: fullPhoneNumber, otp });
    return response.data;
};

export async function validateOtpForLoginAndCheckKYC(phone, otp) {
    console.log("Validating OTP:", phone, otp);

    const trimmedPhone = phone ? phone.trim() : "";
    const trimmedOtp = otp ? otp.trim() : "";

    console.log(`Initiating validation for phone: ${trimmedPhone} with OTP.`);

    if (!trimmedPhone || !trimmedOtp) {
        console.error("Validation Failed: Phone number or OTP is missing.");
        return { success: false, message: 'Phone number and OTP are required.' };
    }

    try {
        const verifyOtpUrl = config.apiUrl + '/ops/invoiceFinancing/verify-otp';
        console.log(`Step 1: Calling ${verifyOtpUrl} for initial OTP check.`);

        const verifyResponse = await http.post(verifyOtpUrl, {
            mobile: trimmedPhone,
            otp: trimmedOtp
        });

        console.log("Step 1 Response (/verify-otp):", verifyResponse.data);

        if (!verifyResponse.data || !verifyResponse.data.success) {
            console.error("Step 1 Failed: OTP verification rejected by backend.");
            if (verifyResponse.data?.authMethod) {
                return {
                    success: false,
                    message: verifyResponse.data.message || "Authentication method mismatch.",
                    authMethod: verifyResponse.data.authMethod,
                    email: verifyResponse.data.email
                };
            }
            return {
                success: false,
                message: verifyResponse.data?.message || "Invalid or expired OTP."
            };
        }

        const { data } = await http.post(config.apiUrl + `/ops/auth/login/validateOtpPersonalLoan`, {
            phone: phone ? phone.trim() : "",
            otp: otp ? otp.trim() : "",
            partnerId: "5e9332dc428935309c11e33b"
        }, { headers: { loggedInFrom: "Madad" } });

        console.log("Raw OTP validation response:", data);

        if (data.success) {
            // Save the entire user object including userType
            SharedCache.set("user", data.user);
            SharedCache.set("token", data.user?._id || "placeholderuserid"); // Using user ID as token for now
            SharedCache.set("partner", data.partner);
            SharedCache.set("isNewUser", data.isNewUser === true);

            // Set the user type based on backend response or a default if not explicitly provided
            if (data.user?.userType) {
                setUserTypeGlobally(data.user.userType);
            } else {
                setUserTypeGlobally('msme'); // Default to 'msme'
            }
        }

        console.log("OTP validation processed, isNewUser:", data.isNewUser);
        return data;
    } catch (error) {
        console.error("Error validating OTP:", error);
        return {
            success: false,
            message: error.response?.data?.message || "Failed to validate OTP. Please try again."
        };
    }
}

/**
 * Function to check if user is authenticated
 * @returns {boolean} True if authenticated
 */

export function isAuthenticated() {
    const userType = getUserType(); // Will log from above

    console.log("---- AUTH.JS isAuthenticated() CALLED ----");
    console.log("Effective userType for logic:", userType);
    console.log("Token found in SharedCache:"); // Avoid logging actual token value in production

    if (userType === 'buyer') {
        const buyerData = SharedCache.get("buyerFullData");
        console.log("Buyer auth path - buyerFullData found in SharedCache:", !!buyerData/*, buyerData*/);
        const authenticated = !!buyerData;
        console.log("Buyer auth path - returning:", authenticated);
        return authenticated;
    } else {
        const user = SharedCache.get("user");
        console.log("Non-buyer auth path ('" + userType + "') - user found in SharedCache:", !!user/*, user*/);
        const authenticated = !!user;
        console.log("Non-buyer auth path - returning:", authenticated);
        return authenticated;
    }
}

/**
 * Function to logout user
 * Clears all user data from cache and localStorage, including userType
 */
// src/api/auth.js
export function logout() {
    console.log("Attempting to logout and clear session...");
    SharedCache.clear();
    localStorage.removeItem("userType");
    localStorage.removeItem("buyerFullData");
    localStorage.removeItem("token");
    console.log("Session items cleared from localStorage and SharedCache.");
}

export const registerNewUser = async (userData) => {
    try {
        const response = await fetch(`${config.apiUrl}/ops/invoiceFinancing/registerNewUser`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(userData),
        });

        const data = await response.json();

        if (!response.ok) {
            return {
                success: false,
                message: data.message || 'Failed to register user',
                authMethod: data.authMethod || null
            };
        }

        if (data.success && data.user) {
            SharedCache.set("user", data.user);
            // Set the user type during registration based on what was passed or a default
            if (userData.userType) {
                setUserTypeGlobally(userData.userType);
            } else {
                setUserTypeGlobally('msme'); // Default for new registrations if not specified
            }
        }

        return data;
    } catch (error) {
        console.error('Error registering user:', error);
        return {
            success: false,
            message: 'Network error while registering user'
        };
    }
};

export const emailLogin = async (email, password) => {
    console.log("Attempting email login for:", email);
    let msmeLoginData = null;
    let buyerLoginData = null;
    let msmeLoginError = null;

    // --- Attempt 1: Login as InvoiceFinancingUser (MSME) ---
    try {
        const msmeResponse = await fetch(`${config.apiUrl}/ops/invoiceFinancing/login/password`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ email, password }),
        });
        msmeLoginData = await msmeResponse.json();

        if (msmeResponse.ok && msmeLoginData.success && msmeLoginData.user) {
            console.log("Email Login: MSME login successful.");
            // Prioritize MSME if successful
            SharedCache.set("user", msmeLoginData.user);
            SharedCache.set("msmeUser", msmeLoginData.user); // Store MSME specific profile
            SharedCache.set("buyerFullData", null);      // Initialize buyer as null
            if (msmeLoginData.token) SharedCache.set("token", msmeLoginData.token);
            setUserTypeGlobally('msme'); // Active type is MSME

            return {
                success: true,
                user: msmeLoginData.user,
                token: msmeLoginData.token,
                type: 'msme', // Explicitly return type
            };
        } else {
            console.log("Email Login: MSME login failed or user not found via MSME endpoint.", msmeLoginData?.message);
            // Store the error/data if MSME login wasn't successful but didn't throw network error
            msmeLoginError = msmeLoginData;
        }
    } catch (error) {
        msmeLoginError = error; // Catch network or other exceptions
        console.warn("Email Login: MSME login attempt caught an error:", error.message);
    }

    // --- Attempt 2: Login as SuperAdmin (BuyerAdmin) if MSME login failed ---
    // Only attempt buyer login if MSME login did not succeed with a valid user
    // (msmeLoginData.success would be false or msmeLoginData.user would be null)
    console.log("Email Login: Proceeding to check for Buyer Admin login.");
    try {
        const buyerResponse = await fetch(`${config.apiUrl}/ops/invoiceFinancing/superadmin/login`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ username: email, password }), // SuperAdmin API expects 'username'
        });
        buyerLoginData = await buyerResponse.json();

        if (buyerResponse.ok && buyerLoginData.success && buyerLoginData.superadmin) {
            if (buyerLoginData.superadmin.role === 'buyerAdmin') {
                console.log("Email Login: Buyer Admin login successful.");
                SharedCache.set("user", buyerLoginData.superadmin);
                SharedCache.set("buyerFullData", buyerLoginData.superadmin); // Store Buyer specific profile
                SharedCache.set("msmeUser", null); // Initialize MSME as null
                if (buyerLoginData.token) SharedCache.set("token", buyerLoginData.token);
                setUserTypeGlobally('buyer'); // Active type is Buyer

                return {
                    success: true,
                    user: buyerLoginData.superadmin,
                    token: buyerLoginData.token,
                    type: 'buyer', // Explicitly return type
                };
            } else {
                // Logged in as another superadmin role (not 'buyerAdmin')
                console.log(`Email Login: SuperAdmin login successful for role: ${buyerLoginData.superadmin.role}. Treating as non-standard.`);
                SharedCache.set("user", buyerLoginData.superadmin);
                SharedCache.set("msmeUser", null);
                SharedCache.set("buyerFullData", null);
                if (buyerLoginData.token) SharedCache.set("token", buyerLoginData.token);
                setUserTypeGlobally(buyerLoginData.superadmin.role); // Set specific admin role
                return {
                    success: true,
                    user: buyerLoginData.superadmin,
                    token: buyerLoginData.token,
                    type: buyerLoginData.superadmin.role
                };
            }
        } else {
            console.log("Email Login: Buyer Admin login failed or user not found.", buyerLoginData?.message);
        }
    } catch (error) {
        console.error("Email Login: Buyer Admin login attempt caught an error:", error.message);
    }

    // If both attempts failed, return the most relevant error (e.g., from MSME attempt if it had a message)
    const finalErrorData = msmeLoginError || buyerLoginData || { success: false, message: "Invalid credentials or user not found." };
    return {
        success: false,
        message: finalErrorData.message || "Login failed. Please check your credentials.",
        // type: null // Or some default error type
    };
};

export const emailSignup = async (email, password, confirmPassword, userType = 'msme') => {
    try {
        const response = await fetch(`${config.apiUrl}/ops/invoiceFinancing/signup/mail`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ email, password, confirmPassword, userType }), // Pass userType to signup
        });

        const data = await response.json();

        console.log("USER HERE FROM SIGN UP", data);

        if (data.success) {
            // Save the entire user object including userType
            SharedCache.set("user", data.user);
            await fetch(`${config.apiUrl}/ops/invoiceFinancing/send-verification-email`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ email, baseUrl: `https://madad-msme.fundfina.com` + `/onBoarding?email=${email}` }),
            });

            if (data.token) {
                localStorage.setItem('token', data.user?._id || "placeholderuserid");
            }
            // Set user type after successful signup
            if (data.user?.userType) {
                setUserTypeGlobally(data.user.userType);
            } else {
                setUserTypeGlobally(userType); // Use the type passed to the function or 'msme'
            }

            // Fetch and store full buyer data if the user is a buyer
            if (data.user?.userType === 'buyer' && data.user?.email) {
                const buyerFullData = await fetchBuyerData(data.user.email);
                if (buyerFullData) {
                    localStorage.setItem('buyerFullData', JSON.stringify(buyerFullData));
                    console.log("Stored buyer full data:", buyerFullData);
                }
            }
        }

        return data;
    } catch (error) {
        console.error("API error during email signup:", error);
        return {
            success: false,
            message: "Network error. Please check your connection and try again."
        };
    }
}


export async function fetchInvoiceById(invoiceId) {
    try {
        const token = SharedCache.get("token");
        const response = await http.get(`${config.apiUrl}/ops/invoiceFinancing/fetchInvoice/${invoiceId}`, {
            headers: {
                'x-auth-token': token
            }
        });
        return response.data;
    } catch (error) {
        console.error('Error fetching invoice:', error);
        throw error;
    }
}

/**
 * Function to fetch user profile
 * @returns {Promise<Object>} User profile data
 */
export async function fetchUserProfile() {
    try {
        const token = SharedCache.get("token");
        const user = SharedCache.get("user");
        const userId = user?._id || user?.id; // Allow for both _id and id

        if (!token || !userId) {
            throw new Error('User not authenticated');
        }

        const response = await http.get(`${config.apiUrl}/ops/user/profile/${userId}`, {
            headers: {
                'x-auth-token': token
            }
        });
        return response.data;
    } catch (error) {
        console.error('Error fetching user profile:', error);
        throw error;
    }
}

export const verifyEmail = async (token) => {
    try {
        const response = await fetch(`${config.apiUrl}/ops/invoiceFinancing/verify-email?token=${token}`, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
            }
        });

        const data = await response.json();

        if (data.success) {
            // Save the entire user object including userType
            SharedCache.set("user", data.user);

            if (data.token) {
                localStorage.setItem('token', data.user?._id || "placeholderuserid");
            }
            if (data.user?.userType) {
                setUserTypeGlobally(data.user.userType);
            } else {
                setUserTypeGlobally('msme'); // Default if not provided
            }

            // Fetch and store full buyer data if the user is a buyer
            if (data.user?.userType === 'buyer' && data.user?.email) {
                const buyerFullData = await fetchBuyerData(data.user.email);
                if (buyerFullData) {
                    localStorage.setItem('buyerFullData', JSON.stringify(buyerFullData));
                    console.log("Stored buyer full data:", buyerFullData);
                }
            }
        }

        return data;
    } catch (error) {
        console.error("API error during email signup:", error);
        return {
            success: false,
            message: "Network error. Please check your connection and try again."
        };
    }
}

export const sendOtpEmail = async (email) => {
    try {
        const response = await fetch(`${config.apiUrl}/ops/invoiceFinancing/send-otp-email`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ email }),
        });

        const data = await response.json();

        return data;
    } catch (error) {
        console.error("API error during email signup:", error);
        return {
            success: false,
            message: "Network error. Please check your connection and try again."
        };
    }
}

export const verifyOtpEmail = async (email, otp) => {
    try {
        const response = await fetch(`${config.apiUrl}/ops/invoiceFinancing/verify-otp-email`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ email, otp }),
        });

        const data = await response.json();
        return data;
    } catch (error) {
        console.error("API error during OTP verification:", error);
        return {
            success: false,
            message: "Network error. Please try again."
        };
    }
};


export const fakeOtpSend = async (phone) => {
    try {
        // const response = await fetch(`https://2factor.in/API/V1/c2c53ef5-1621-11e8-a895-0200cd936042/SMS/${phone}/AUTOGEN/OTP2`, {
        //     method: 'POST',
        //     headers: {
        //         'Content-Type': 'application/json',
        //     },
        // });

        // const data = await response.json();
        // console.log(data);

        // return data;
        return {};
    } catch (error) {
        console.error("API error during email signup:", error);
        return {
            success: false,
            message: "Network error. Please check your connection and try again."
        };
    }
}

export async function fetchSuperAdminData(email) {
    try {
        // This endpoint should return the SuperAdmin document for the given email
        // It might need authentication if it's a protected endpoint
        const token = SharedCache.get("token"); // Assuming a token for SuperAdmin might be needed
        const response = await fetch(`${config.apiUrl}/ops/superadmin/profile?email=${encodeURIComponent(email)}`, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'x-auth-token': token // Send token if endpoint is protected
            },
        });
        const data = await response.json();
        if (response.ok && data.success) {
            return data.user; // Assuming backend returns SuperAdmin data under 'user' key
        } else {
            console.error("Failed to fetch SuperAdmin data:", data.message || "Unknown error");
            return null;
        }
    } catch (error) {
        console.error("Error fetching SuperAdmin data:", error);
        return null;
    }
}

// Export all functions as default and individually
export default {
    fetchOtpForLogin,
    validateOtpForLoginAndCheckKYC,
    isAuthenticated,
    logout,
    fetchInvoiceById,
    fetchUserProfile,
    verifyEmail,
    sendOtpEmail,
    getUserType,
    getCurrentUser,
    fetchBuyerData,
    fetchSuperAdminData
};