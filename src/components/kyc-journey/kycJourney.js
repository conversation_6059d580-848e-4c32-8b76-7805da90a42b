import React, { useState, useEffect } from 'react';
import { usePara<PERSON>, useHistory } from 'react-router-dom';
import {
    // CheckCircleIcon from outline might be needed if you use it for status
    // import { CheckCircleIcon as OutlineCheckCircleIcon } from '@heroicons/react/24/outline';
    BriefcaseIcon,
    DocumentTextIcon,
    UsersIcon,
    MagnifyingGlassCircleIcon,
    ListBulletIcon,
    ShieldCheckIcon,
    VideoCameraIcon
} from '@heroicons/react/24/outline'; // Changed to outline
import { CheckCircleIcon } from '@heroicons/react/16/solid';

import Header from '../dashboard/Header';
import Footer from '../dashboard/Footer';
import BusinessDetails from './steps/businessDetails';
import FinancialInfo from './steps/financialInfo';
import ReviewKYC from './steps/reviewKYC';
import BusinessDocuments from './steps/businessDocuments';

import PersonalInfo from './steps/personalInfo';
import { getKycInfo, updateKyc } from '../../api/kyc';
import SharedCache from '../../sharedCache';
import Sidebar from '../dashboard/Sidebar';
import FinancialDocuments from './steps/financialsNew';

function classNames(...classes) {
    return classes.filter(Boolean).join(' ');
}

// Define icons for each step. Colors will be applied based on status.
const stepSpecificIcons = {
    'business-documents': (status) => <BriefcaseIcon className="w-5 h-5 text-gray-600" />,
    'financials': (status) => <DocumentTextIcon className="w-5 h-5 text-gray-600" />,
    'shareholders-personal-info': (status) => <MagnifyingGlassCircleIcon className="w-5 h-5 text-gray-600" />, // Changed from UsersIcon
    'top-buyers': (status) => <UsersIcon className="w-5 h-5 text-gray-600" />, // Changed from MagnifyingGlassCircleIcon
    'review': (status) => <ListBulletIcon className="w-5 h-5 text-gray-600" />,
    'personalInfo': (status) => <ShieldCheckIcon className="w-5 h-5 text-gray-600" />, 
    'ekyc': (status) => <VideoCameraIcon className="w-5 h-5 text-gray-600" />, 
};
const KycJourney = () => {
    const { step: routeStep } = useParams();
    const history = useHistory();

    const steps = [
        { id: 'business-documents', label: 'Business Documents' },
        { id: 'financials', label: 'Financials' },
        { id: 'shareholders-personal-info', label: 'Shareholder & Personal Info' }, // Updated to match design
        { id: 'top-buyers', label: 'Top Buyers' },
        { id: 'ekyc', label: 'eKYC' }, // Added to match the 6 steps in design
        { id: 'review', label: 'Review' },
    ];

    const currentStepId = routeStep && steps.some(s => s.id === routeStep) ? routeStep : steps[0]?.id || 'business-documents';
    const currentStepIndex = Math.max(0, steps.findIndex(s => s.id === currentStepId));

    useEffect(() => {
        if (routeStep !== currentStepId && steps.length > 0) {
            history.replace(`/kyc/${currentStepId}`);
        }
    }, [routeStep, currentStepId, history, steps]);

    const [currentPage, setCurrentPage] = useState('dashboard');
    const [formData, setFormData] = useState({});
    const [isLoading, setIsLoading] = useState(true);
    const [error, setError] = useState(null);
    const [kycStatus, setKycStatus] = useState(null);
    const [showTooltip, setShowTooltip] = useState(false);
    const [tooltipPos, setTooltipPos] = useState({ x: 0, y: 0 });

    const isEkycEnabled = kycStatus !== "INITIATED";

    useEffect(() => {
        const fetchKycData = async () => {
            setIsLoading(true);
            setError(null);
            try {
                const user = SharedCache.get("user");
                if (!user || (!user._id && !user.id)) {
                    throw new Error("User information not found. Please log in again.");
                }
                const userId = user._id || user.id;
                const response = await getKycInfo(userId);
                if (response.success && response.kyc) {
                    setKycStatus(response.kyc.verificationStatus);
                    setFormData(prev => ({ ...prev, ...response.kyc, userId, firstName: prev.firstName || user.firstName, lastName: prev.lastName || user.lastName, email: prev.email || user.email }));
                } else {
                    setKycStatus(null);
                    setFormData(prev => ({ ...prev, userId, firstName: prev.firstName || user.firstName, lastName: prev.lastName || user.lastName, email: prev.email || user.email }));
                }
            } catch (err) {
                console.error("Error fetching KYC data:", err);
                setError(err.message || "An error occurred while fetching your data.");
            } finally {
                setIsLoading(false);
            }
        };
        fetchKycData();
    }, []);

    const handleNext = async (stepData) => {
        setIsLoading(true);
        setError(null);
        try {
            const updatedData = { ...formData, ...stepData };
            setFormData(updatedData);
            // if (!updatedData._id) throw new Error("User ID is missing. Cannot save data.");
            // const saveResponse = await updateKyc(updatedData);
            // if (!saveResponse.success) throw new Error(saveResponse.message || "Failed to save KYC data.");

            const nextStepIndex = currentStepIndex + 1;
            if (nextStepIndex < steps.length) {
                history.push(`/kyc/${steps[nextStepIndex].id}`);
            } else {
                history.push('/dashboard?kyc=complete');
            }
        } catch (err) {
            console.error("Error during handleNext:", err);
            setError(err.message || "Failed to save your information.");
        } finally {
            setIsLoading(false);
        }
    };

    const handleBack = () => {
        const prevStepIndex = currentStepIndex - 1;
        if (prevStepIndex >= 0) history.push(`/kyc/${steps[prevStepIndex].id}`);
    };

    const renderStep = () => {
        if (isLoading && formData.userId) {
            return <div className="flex items-center justify-center h-64"><div className="w-12 h-12 border-4 border-green-600 border-t-transparent rounded-full animate-spin"></div></div>;
        }
        switch (currentStepId) {
            case 'business-documents': return <BusinessDocuments onNext={handleNext} initialData={formData} onBack={handleBack} />;
            case 'financials': return <FinancialDocuments onNext={handleNext} onBack={handleBack} initialData={formData} />;
            case 'shareholders-personal-info': return <BusinessDetails onNext={handleNext} onBack={handleBack} initialData={formData} />;
            case 'top-buyers': return <FinancialInfo onNext={handleNext} onBack={handleBack} initialData={formData} />;
            case 'review': return <ReviewKYC onNext={handleNext} onBack={handleBack} data={formData} />;
            case 'ekyc': return <PersonalInfo onNext={handleNext} onBack={handleBack} data={formData} />;
            default: return <div className="text-center p-8">Step not found.</div>;
        }
    };

    const getStepStatus = (index) => {
        if (index < currentStepIndex) return 'completed';
        if (index === currentStepIndex) return 'current';
        return 'pending';
    };

    return (
        <div className="min-h-screen bg-[#fafafa] flex flex-col bg-gray-100">
            <div className=" bg-[#fafafa] flex flex-1 h-[calc(100vh-var(--header-height,64px)-var(--footer-height,56px))]">
                <div className="flex flex-col lg:flex-row px-4 lg:px-8 py-4 lg:py-12 w-full gap-4">
                    <aside
                        className="bg-white rounded-lg shadow-md pt-6 border border-gray-100 w-full lg:w-[20vw] lg:min-w-[200px] lg:max-w-[350px] self-start"
                    >
                        <div>
                            <div className='px-4'>
                                <p className="text-[12px] text-gray-500 uppercase tracking-wide mb-1">
                                    LET'S FINISH SETTING UP YOUR ACCOUNT
                                </p>
                                <h3 className="text-xl font-semibold text-gray-900 mb-3 leading-snug">
                                    You are just {Math.max(1, steps.length - currentStepIndex)} {steps.length - currentStepIndex === 1 ? 'step' : 'steps'} away from discounting your invoices
                                </h3>

                                {/* Progress Bar */}
                                <div className="flex gap-2 mb-8">
                                    {steps.map((_, index) => (
                                        <div
                                            key={index}
                                            className={classNames(
                                                "h-2 flex-1 rounded-full",
                                                index <= currentStepIndex ? 'bg-green-500' : 'bg-gray-300'
                                            )}
                                        />
                                    ))}
                                </div>
                            </div>

                            {/* Steps Navigation */}
                            <nav className="space-y-0">
                                {/* Desktop: Show all steps, Mobile: Show only current step */}
                                {steps.map((s, index) => {
                                    const status = getStepStatus(index);
                                    const isThisEkycStep = s.id === 'ekyc';
                                    const ekycRelatedDisabled = isThisEkycStep && !isEkycEnabled;
                                    const isCurrentStep = status === 'current';

                                    // On mobile (lg:block hidden), hide non-current steps
                                    const shouldShowOnMobile = isCurrentStep;

                                    const LeftIconDisplay = () => {
                                        if (status === 'completed') {
                                            return <CheckCircleIcon className="w-6 h-6 text-green-500" />;
                                        } else if (status === 'current') {
                                            return <div className="w-6 h-6 border-2 border-gray-300 rounded-full"></div>;
                                        } else {
                                            return <div className="w-6 h-6 border-2 border-gray-300 rounded-full"></div>;
                                        }
                                    };

                                    const StepIcon = stepSpecificIcons[s.id];

                                    return (
                                        <button
                                            key={s.id}
                                            onClick={(e) => {
                                                if (ekycRelatedDisabled) {
                                                    const buttonRect = e.currentTarget.getBoundingClientRect();
                                                    setTooltipPos({ x: buttonRect.left + window.scrollX, y: buttonRect.bottom + window.scrollY + 6 });
                                                    setShowTooltip(true);
                                                    setTimeout(() => setShowTooltip(false), 3000);
                                                    return;
                                                }
                                                if (status !== 'current') history.push(`/kyc/${s.id}`);
                                            }}
                                            disabled={ekycRelatedDisabled}
                                            className={classNames(
                                                "w-full flex items-center px-4 py-3 text-left transition-colors duration-150 ease-in-out focus:outline-none focus-visible:ring-2 focus-visible:ring-green-500",
                                                "border-b border-gray-100 last:border-b-0",
                                                status === 'current' ? 'bg-[#f5f5f5]' : 'bg-white hover:bg-gray-50',
                                                ekycRelatedDisabled ? 'cursor-not-allowed opacity-70' : 'cursor-pointer',
                                                // Mobile responsive classes: show only current step on mobile
                                                shouldShowOnMobile ? 'block' : 'hidden lg:flex'
                                            )}
                                            aria-current={status === 'current' ? 'step' : undefined}
                                        >
                                            <div className="flex items-center flex-1">
                                                <div className="mr-4">
                                                    {StepIcon && StepIcon(status)}
                                                </div>
                                                <span className={classNames(
                                                    'text-base font-xs',
                                                    status === 'current' ? 'text-gray-900' : 'text-gray-700',
                                                    status === 'pending' ? 'text-gray-500' : ''
                                                )}>
                                                    {s.label}
                                                </span>
                                            </div>
                                            <div className="ml-4">
                                                <LeftIconDisplay />
                                            </div>
                                        </button>
                                    );
                                })}
                            </nav>

                            {showTooltip && ( /* eKYC Tooltip */
                                <div className="fixed z-[100] bg-gray-900 text-white text-xs px-3 py-1.5 rounded-md shadow-lg pointer-events-none" style={{ top: tooltipPos.y, left: tooltipPos.x }}>
                                    eKYC is currently disabled for your account.
                                </div>
                            )}
                        </div>
                    </aside>
                    <main className="flex-1 bg-[#fafafa] overflow-y-auto">
                        {error && formData.userId && ( /* Step processing error display */
                            <div className="bg-red-50 border-l-4 border-red-500 text-red-700 mb-6 rounded-md shadow" role="alert">
                                <div className="flex">
                                    <div className="py-1"><svg className="fill-current h-6 w-6 text-red-500 mr-3" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20"><path d="M2.93 17.07A10 10 0 1 1 17.07 2.93 10 10 0 0 1 2.93 17.07zM11 14v-4a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v4a1 1 0 0 0 1 1h2a1 1 0 0 0 1-1zm0-8a1 1 0 1 0-2 0 1 1 0 0 0 2 0z" /></svg></div>
                                    <div>
                                        <p className="font-semibold">An error occurred</p>
                                        <p className="text-sm">{error}</p>
                                        <button className="mt-2 text-xs text-red-600 hover:text-red-800 font-semibold" onClick={() => setError(null)} aria-label="Dismiss error">Dismiss</button>
                                    </div>
                                </div>
                            </div>
                        )}
                        {renderStep()}
                    </main>
                </div>
            </div>
        </div>
    );
};

export default KycJourney;