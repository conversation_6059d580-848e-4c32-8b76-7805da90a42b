import React, { useState, useEffect, useRef, useCallback } from 'react';
import { useHistory } from 'react-router-dom';
import SharedCache from '../../../sharedCache'; // For User ID
import config from '../../../config.json'; // For API URL
// Assuming api/kyc exports required functions including shareholder and buyer updates
import {
    getKycInfo,
    updateKyc,
    uploadKycDocument,
    updateShareholderTextData,
    uploadShareholderDocument,
    updateBuyers // <--- Import updateBuyers
} from '../../../api/kyc';
import Modal from 'react-modal'; // For document/consent modals
import { shortenDocumentName } from '../../../components/utils';

// --- Import Heroicons ---
import {
    EyeIcon,            // View
    ArrowPathIcon,      // Replace Placeholder / Loading
    XMarkIcon,          // Close / Cancel
    CheckCircleIcon,    // Uploaded Check
    InformationCircleIcon, // Info/Consent
    ExclamationTriangleIcon, // Error
    ChevronUpIcon,   // Generic Document
    ChevronDownIcon,     // Person/User
    BuildingOffice2Icon, // Business
    UsersIcon,          // Shareholders/Buyers/Directors
    PencilSquareIcon,   // Edit
    CheckIcon,          // Save
    ArrowUpTrayIcon,    // Upload Trigger
    NoSymbolIcon        // Not Provided / Empty
} from '@heroicons/react/24/outline';
import LoadingModal from '../../Reusable/Loading';

// --- Helper Functions (Keep existing helpers: getFileNameFromPath, formatDate, formatDateForInput, deepClone, getNestedValue) ---
const getFileNameFromPath = (path) => {
    if (!path) return 'N/A';
    try {
        const decodedPath = decodeURIComponent(path);
        const filename = decodedPath.substring(decodedPath.lastIndexOf('/') + 1);
        return filename || (decodedPath.includes('/') ? 'N/A' : decodedPath) || 'N/A';
    } catch (e) {
        console.error("Error parsing filename:", path, e);
        return path || 'Invalid Name';
    }
};

const formatDate = (dateString) => {
    if (!dateString) return '-';
    const date = typeof dateString === 'string' ? new Date(dateString) : dateString;
    if (isNaN(date?.getTime()) || date?.getFullYear() <= 1970) return '-';
    try {
        return date.toLocaleDateString('en-GB', {
            day: '2-digit', month: 'short', year: 'numeric'
        }).replace(/ /g, '-');
    } catch (e) {
        console.error("Error formatting date:", dateString, e);
        return 'Invalid Date';
    }
};

const LoadingPopup = ({ fileName }) => {
    return <LoadingModal />
};


// --- Add/Ensure these Icons for Verification Status Badge ---

// Icon for Pending (Clock - Outline)
const ClockIcon = () => (
    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={2}>
        <path strokeLinecap="round" strokeLinejoin="round" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
    </svg>
);

// Icon for Rejected (X Circle - Solid)
const XCircleIcon = () => (
    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="currentColor" viewBox="0 0 20 20">
        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
    </svg>
);


const formatDateForInput = (dateString) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    if (isNaN(date.getTime())) return '';
    try {
        const year = date.getFullYear();
        const month = (date.getMonth() + 1).toString().padStart(2, '0');
        const day = date.getDate().toString().padStart(2, '0');
        return `${year}-${month}-${day}`;
    } catch (e) {
        console.error("Error formatting date for input:", dateString, e);
        return '';
    }
};

// --- Expiry Date Validation Function ---
const isExpiryDateValid = (dateString) => {
    if (!dateString || typeof dateString !== 'string') {
        console.warn("[isExpiryDateValid] Invalid or missing dateString input.");
        return false;
    }

    // Attempt to parse DD/MM/YYYY or DD-MM-YYYY first
    const ddMmYyyyMatch = dateString.match(/^(\d{1,2})[/-](\d{1,2})[/-](\d{4})$/);
    let parsedExpiryDate = null;

    if (ddMmYyyyMatch) {
        const [_, day, month, year] = ddMmYyyyMatch;
        // JavaScript Date months are 0-indexed (0 for January, 11 for December)
        parsedExpiryDate = new Date(year, month - 1, day);
    } else {
        // Fallback to standard Date parsing for other formats like YYYY-MM-DD
        parsedExpiryDate = new Date(dateString);
    }

    // Check if parsing resulted in a valid date
    if (isNaN(parsedExpiryDate.getTime())) {
        console.warn(`[isExpiryDateValid] Failed to parse dateString: "${dateString}" into a valid date.`);
        return false;
    }

    // Normalize expiry date to midnight to compare dates accurately
    parsedExpiryDate.setHours(0, 0, 0, 0);

    const today = new Date();
    today.setHours(0, 0, 0, 0); // Normalize today to midnight

    // 1. Check if the document is already expired
    if (parsedExpiryDate < today) {
        console.log(`[isExpiryDateValid] Date ${parsedExpiryDate.toDateString()} is in the past (expired). Today: ${today.toDateString()}`);
        return false; // Expired
    }

    // 2. Calculate the date 3 months from today
    const threeMonthsFromToday = new Date(today);
    threeMonthsFromToday.setMonth(today.getMonth() + 3);

    if (parsedExpiryDate <= threeMonthsFromToday) {
        console.log(`[isExpiryDateValid] Date ${parsedExpiryDate.toDateString()} expires on or before ${threeMonthsFromToday.toDateString()} (within 3 months).`);
        return false; // Expiring within 3 months or exactly on the 3-month mark
    }

    // 3. If none of the above, the document expires after more than 3 months
    console.log(`[isExpiryDateValid] Date ${parsedExpiryDate.toDateString()} is valid (expires after more than 3 months from ${today.toDateString()}).`);
    return true;
};

const deepClone = (obj) => {
    try {
        return JSON.parse(JSON.stringify(obj));
    } catch (e) {
        console.error("Deep clone error:", e);
        return null;
    }
};

const getNestedValue = (obj, path, defaultValue = '') => {
    if (path == "cashFlowLedger") {
        console.log('object aasd', obj, path, defaultValue);
    }
    if (!obj || !path) return defaultValue;
    const value = path.split('.').reduce((o, k) => o?.[k], obj);
    if (path == "cashFlowLedger") {
        console.log('object aasd', obj, path, defaultValue, value);
    }
    console.log(value, "HERE", path);
    return value ?? defaultValue;
};

// --- Review Page Component ---
const ReviewAndSubmit = ({ onBack }) => {
    const [userData, setUserData] = useState(null);
    const [formData, setFormData] = useState(null);
    const [isLoading, setIsLoading] = useState(true);
    const [loadError, setLoadError] = useState('');
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [submitError, setSubmitError] = useState('');
    const [isSaving, setIsSaving] = useState(false);
    const [saveError, setSaveError] = useState('');
    const [uploadingDoc, setUploadingDoc] = useState({});
    const [kycStatus, setKycStatus] = useState(null);
    const history = useHistory();
    const [missingFieldLabels, setMissingFieldLabels] = useState([]);
    const fileInputRefs = useRef({});

    const [agreedAccurate, setAgreedAccurate] = useState(false);
    const [readTerms, setReadTerms] = useState(false);
    const [readFinancialConsent, setReadFinancialConsent] = useState(false);

    const [expandedSections, setExpandedSections] = useState({
        businessDetails: true, // Start business details expanded
        personalDetails: false,
        bankDetails: false,
        uploadedDocuments: false,
        // Shareholders, Buyers, Directors sections will be handled differently
    });

    const toggleSectionExpand = (sectionKey) => {
        setExpandedSections(prev => ({
            ...prev,
            [sectionKey]: !prev[sectionKey]
        }));
    };

    const [docViewerUrl, setDocViewerUrl] = useState('');
    const [isDocViewerOpen, setIsDocViewerOpen] = useState(false);
    const [isConsentModalOpen, setIsConsentModalOpen] = useState(false);
    const [editingSectionKey, setEditingSectionKey] = useState(null); // null, 'businessDetails', 'personalDetails', 'bankDetails', 'uploadedDocuments'
    const [currentSavingSection, setCurrentSavingSection] = useState(null); // To show spinner on the correct save button
    // --- Document Structure Helper (Keep existing) ---
    const ensureDocStructure = (doc) => {
        if (doc && typeof doc === 'object' && doc.filePath !== undefined) {
            return {
                filePath: doc.filePath || null,
                signedUrl: doc.signedUrl || null,
                uploadedOn: doc.uploadedOn,
                mimeType: doc.mimeType,
                verificationStatus: doc.verificationStatus,
                verificationNotes: doc.verificationNotes,
                verifiedOrRejectedOn: doc.verifiedOrRejectedOn,
                verifiedOrRejectedBy: doc.verifiedOrRejectedBy,
            };
        } else if (typeof doc === 'string') {
            return { filePath: doc, signedUrl: null };
        }
        return { filePath: null, signedUrl: null };
    };

    // New handler to toggle edit mode for a specific section
    const handleEditSectionToggle = (sectionKey) => {
        setSaveError(''); // Clear any previous save errors
        if (editingSectionKey === sectionKey) {
            // If clicking on the already editing section's "Cancel" or "Done" button
            setFormData(deepClone(userData)); // Revert changes for that section from original data
            setEditingSectionKey(null);
        } else if (editingSectionKey !== null) {
            // If another section is already being edited, prompt or auto-cancel
            alert("Please save or cancel edits in the current section before editing another.");
        } else {
            // No section is being edited, so start editing the new one
            setFormData(deepClone(userData)); // Ensure formData is a fresh copy for editing
            setEditingSectionKey(sectionKey);
        }
    };

    // New handler to cancel edits for the currently active section
    const handleCancelSectionEdit = (sectionKey) => {
        setSaveError('');
        if (userData) {
            setFormData(deepClone(userData)); // Revert to original data
        }
        setEditingSectionKey(null); // Exit editing mode for this section
        // Reset any other relevant temporary states for this section if needed
    };

    // New handler to save changes for a specific section
    const handleSaveSection = async (sectionKey) => {
        if (!formData || !userData) {
            setSaveError("Data not loaded correctly. Cannot save.");
            return;
        }
        const userId = userData._id || userData.id;
        if (!userId) {
            setSaveError("User ID is missing. Cannot save.");
            return;
        }

        setIsSaving(true);
        setCurrentSavingSection(sectionKey);
        setSaveError('');
        let success = false;
        let updatedUserDataFromServer = null;

        try {
            let payload = { id: userId };
            let specificDataToSave = {};

            // Construct payload based on the section being saved
            if (sectionKey === 'businessDetails') {
                specificDataToSave = {
                    // Root level fields that might be part of "business" context
                    licenseNumber: formData.licenseNumber,
                    // email: formData.email, // This might be primary applicant email, clarify if it's business
                    // contactNumber: formData.contactNumber, // Clarify if business contact
                    kyc: {
                        businessDetails: formData.kyc?.businessDetails || {},
                        // Include other relevant parts of kyc if they belong to business details
                        // e.g., isQatarBased is inside kyc.businessDetails in your render logic
                    }
                };
                // Add direct root fields if they are part of this section in formData
                if (formData.email) specificDataToSave.email = formData.email;
                if (formData.contactNumber) specificDataToSave.contactNumber = formData.contactNumber;


            } else if (sectionKey === 'personalDetails') {
                specificDataToSave = {
                    firstName: formData.firstName,
                    middleName: formData.middleName,
                    lastName: formData.lastName,
                    mobileNo: formData.mobileNo, // Assuming this is personal contact
                    // email: formData.email, // Already primary applicant email, usually root
                    kyc: {
                        qidNumber: formData.kyc?.qidNumber,
                        qidExpiryDate: formData.kyc?.qidExpiryDate,
                        nationality: formData.kyc?.nationality,
                        // address: formData.kyc?.address // If personal address is distinct
                    }
                };
                if (formData.email) specificDataToSave.email = formData.email; // ensure primary email is saved

            } else if (sectionKey === 'bankDetails') {
                specificDataToSave = {
                    kyc: {
                        incomeDetails: formData.kyc?.incomeDetails || {}
                    }
                };
            } else {
                console.warn("Unknown section key for save:", sectionKey);
                setIsSaving(false);
                setCurrentSavingSection(null);
                return;
            }

            // Merge section-specific data into the main payload
            // This needs careful handling to match your updateKyc API expectations
            // For example, if updateKyc expects the full user object with modifications:
            payload = deepClone(formData); // Start with current form state
            payload.id = userId;
            // Overwrite only the relevant parts based on sectionKey
            if (sectionKey === 'businessDetails') {
                payload.licenseNumber = specificDataToSave.licenseNumber;
                payload.email = specificDataToSave.email; // Assuming business email is root 'email'
                payload.contactNumber = specificDataToSave.contactNumber; // Assuming business contactNumber is root 'contactNumber'
                payload.kyc = { ...payload.kyc, businessDetails: specificDataToSave.kyc.businessDetails };
            } else if (sectionKey === 'personalDetails') {
                payload.firstName = specificDataToSave.firstName;
                payload.middleName = specificDataToSave.middleName;
                payload.lastName = specificDataToSave.lastName;
                payload.mobileNo = specificDataToSave.mobileNo; // Personal mobile
                payload.email = specificDataToSave.email; // Primary email
                payload.kyc = {
                    ...payload.kyc,
                    qidNumber: specificDataToSave.kyc.qidNumber,
                    qidExpiryDate: specificDataToSave.kyc.qidExpiryDate,
                    nationality: specificDataToSave.kyc.nationality,
                };
            } else if (sectionKey === 'bankDetails') {
                payload.kyc = { ...payload.kyc, incomeDetails: specificDataToSave.kyc.incomeDetails };
            }
            // Ensure shareholders and buyers are not accidentally sent unless intended by that section's save
            // For section-specific saves, you'd typically exclude them.
            payload.shareholders = undefined; // Or send original if not editing them
            if (payload.kyc) payload.kyc.buyers = undefined; // Or send original


            console.log(`Saving ${sectionKey} with payload:`, JSON.stringify(payload, null, 2));
            const response = await updateKyc(payload); // Or a more specific API endpoint

            if (response?.success) {
                console.log(`${sectionKey} update successful:`, response);
                success = true;
                updatedUserDataFromServer = response.user ? deepClone(response.user) : deepClone(payload); // Use response if available
                // Ensure non-edited parts are not lost if backend returns minimal data
                if (response.user) {
                    const freshUserData = await getKycInfo(userId); // Re-fetch complete data
                    if (freshUserData?.success && freshUserData?.user) {
                        updatedUserDataFromServer = deepClone(freshUserData.user);
                    } else {
                        // Fallback to merging if re-fetch fails
                        updatedUserDataFromServer = { ...deepClone(userData), ...updatedUserDataFromServer };
                    }
                }


            } else {
                throw new Error(response?.message || `Failed to save ${sectionKey}.`);
            }

        } catch (error) {
            console.error(`Error saving ${sectionKey}:`, error);
            setSaveError(`Failed to save ${sectionKey}: ${error.message}`);
        } finally {
            setIsSaving(false);
            setCurrentSavingSection(null);
            if (success && updatedUserDataFromServer) {
                setUserData(updatedUserDataFromServer);
                setFormData(deepClone(updatedUserDataFromServer));
                setEditingSectionKey(null); // Exit edit mode for the section
            }
        }
    };

    // --- Data Fetching (Keep existing) ---
    const fetchData = useCallback(async (userId) => {
        setIsLoading(true);
        setLoadError('');
        try {
            console.log(`Workspaceing KYC info for review: userId=${userId}`); // Corrected log message
            const response = await getKycInfo(userId);
            console.log("getKycInfo Response:", response);

            if (response?.success && response?.user) {
                let data = response.user;

                if (response.user.kyc.verificationStatus) {
                    setKycStatus(response.user.kyc.verificationStatus);
                } else {
                    setKycStatus(null); // Or a default value
                }

                // Deep Defaults & Structure Enforcement
                data = data || {};
                data.kyc = data.kyc || {};
                data.kyc.businessDetails = data.kyc.businessDetails || {};
                data.kyc.employmentDetails = data.kyc.employmentDetails || {};
                data.kyc.incomeDetails = data.kyc.incomeDetails || {};
                if (data.commercialRegistration && data.commercialRegistration.extractedFields) {
                    const extracted = data.commercialRegistration.extractedFields;

                    // For Trade Name / Legal Entity Name
                    if (extracted.trade_name !== undefined && (data.kyc.businessDetails.businessName === undefined || data.kyc.businessDetails.businessName === null || data.kyc.businessDetails.businessName === '')) {
                        data.kyc.businessDetails.businessName = extracted.trade_name;
                    }
                    // For CR Number
                    if (extracted.commercial_reg_number !== undefined && (data.kyc.businessDetails.crNumber === undefined || data.kyc.businessDetails.crNumber === null || data.kyc.businessDetails.crNumber === '')) {
                        data.kyc.businessDetails.crNumber = extracted.commercial_reg_number;
                    }
                    // For CR Issue Date
                    const parsedCrIssueDate = parseDMYtoYMD(extracted.issue_date);
                    if (parsedCrIssueDate && (data.kyc.businessDetails.crIssueDate === undefined || data.kyc.businessDetails.crIssueDate === null || data.kyc.businessDetails.crIssueDate === '')) {
                        data.kyc.businessDetails.crIssueDate = parsedCrIssueDate;
                    } else if (extracted.issue_date && (data.kyc.businessDetails.crIssueDate === undefined || data.kyc.businessDetails.crIssueDate === null || data.kyc.businessDetails.crIssueDate === '')) {
                        // Fallback to original string if parsing fails but you still want to populate it
                        data.kyc.businessDetails.crIssueDate = extracted.issue_date;
                    }

                    // For CR Expiry Date
                    const parsedCrExpiryDate = parseDMYtoYMD(extracted.expiry_date);
                    if (parsedCrExpiryDate && (data.kyc.businessDetails.crExpiryDate === undefined || data.kyc.businessDetails.crExpiryDate === null || data.kyc.businessDetails.crExpiryDate === '')) {
                        data.kyc.businessDetails.crExpiryDate = parsedCrExpiryDate;
                    } else if (extracted.expiry_date && (data.kyc.businessDetails.crExpiryDate === undefined || data.kyc.businessDetails.crExpiryDate === null || data.kyc.businessDetails.crExpiryDate === '')) {
                        // Fallback to original string if parsing fails
                        data.kyc.businessDetails.crExpiryDate = extracted.expiry_date;
                    }

                    // For Tax Reg. No.
                    if (extracted.tax_reg_number !== undefined && (data.kyc.businessDetails.taxRegNo === undefined || data.kyc.businessDetails.taxRegNo === null || data.kyc.businessDetails.taxRegNo === '')) {
                        data.kyc.businessDetails.taxRegNo = extracted.tax_reg_number;
                    }
                }
                data.shareholders = Array.isArray(data.shareholders) ? data.shareholders : [];
                // *** Ensure buyers array exists and has correct structure ***
                data.kyc.buyers = Array.isArray(data.kyc.buyers) ? data.kyc.buyers.map(b => ({
                    ...b,
                })) : [];
                data.kyc.directors = Array.isArray(data.kyc.directors) ? data.kyc.directors : [];
                data.authorizedSignatories = Array.isArray(data.authorizedSignatories) ? data.authorizedSignatories : [];
                data.beneficialOwners = Array.isArray(data.beneficialOwners) ? data.beneficialOwners : [];
                data.kyc.address = data.kyc.address || {};
                data.kyc.businessDetails = data.kyc.businessDetails || {};
                data.shareholders.forEach(sh => { sh.address = sh.address || {}; });

                // Document Defaults (Existing ones + shareholder docs)
                data.commercialRegistration = ensureDocStructure(data.commercialRegistration);
                data.tradeLicense = ensureDocStructure(data.tradeLicense);
                data.taxCard = ensureDocStructure(data.taxCard);
                data.establishmentCard = ensureDocStructure(data.establishmentCard);
                data.bankStatement = ensureDocStructure(data.bankStatement);
                data.auditedFinancialReport = ensureDocStructure(data.auditedFinancialReport);
                data.cashFlowLedger = ensureDocStructure(data.cashFlowLedger);
                data.commercialCreditReport = ensureDocStructure(data.commercialCreditReport);
                data.memorandumOfAssociation = ensureDocStructure(data.memorandumOfAssociation);
                data.articleOfAssociation = ensureDocStructure(data.articleOfAssociation);
                data.otherDocument = ensureDocStructure(data.otherDocument);
                data.otherDocumentTwo = ensureDocStructure(data.otherDocumentTwo);
                data.otherDocument3 = ensureDocStructure(data.otherDocument3);
                data.otherDocument4 = ensureDocStructure(data.otherDocument3);
                data.otherDocument5 = ensureDocStructure(data.otherDocument5);
                data.otherDocument6 = ensureDocStructure(data.otherDocument6);
                data.otherDocument7 = ensureDocStructure(data.otherDocument7);
                data.otherDocument8 = ensureDocStructure(data.otherDocument8);
                data.otherDocument9 = ensureDocStructure(data.otherDocument9);
                data.otherDocument10 = ensureDocStructure(data.otherDocument10);
                data.invoiceAdditionalDoc1 = ensureDocStructure(data.invoiceAdditionalDoc1);
                data.invoiceAdditionalDoc2 = ensureDocStructure(data.invoiceAdditionalDoc2);
                data.kyc.qatariId = ensureDocStructure(data.kyc.qatariId);
                data.kyc.passport = ensureDocStructure(data.kyc.passport);
                data.kyc.utilityBill = ensureDocStructure(data.kyc.utilityBill);
                if (!data.kyc.incomeDetails) data.kyc.incomeDetails = {};
                if (!data.kyc.employmentDetails) data.kyc.employmentDetails = {};
                data.kyc.incomeDetails.proofOfIncome = ensureDocStructure(data.kyc.incomeDetails.proofOfIncome);
                data.kyc.employmentDetails.employmentLetter = ensureDocStructure(data.kyc.employmentDetails.employmentLetter);
                data.shareholders.forEach(sh => {
                    sh.passport = ensureDocStructure(sh.passport);
                    sh.qid = ensureDocStructure(sh.qid);
                    sh.proofOfAddress = ensureDocStructure(sh.proofOfAddress);
                    sh.address = sh.address || {};
                });

                const clonedData = deepClone(data);
                if (clonedData) {
                    setUserData(clonedData);
                    setFormData(deepClone(clonedData));
                    console.log("User data set for review (structured):", clonedData);
                } else {
                    setLoadError("Failed to process user data after fetching.");
                    console.error("Deep cloning failed after structuring data.");
                }
            } else {
                setLoadError(response?.message || "Could not retrieve user data.");
            }
        } catch (error) {
            console.error("Error fetching data for review:", error);
            setLoadError(`Error loading data: ${error.message}. Please try again.`);
        } finally {
            setIsLoading(false);
        }
    }, []);

    useEffect(() => {
        const user = SharedCache.get('user') || {};
        const userId = user._id || user.id || "";

        if (!userId) {
            setLoadError("User ID not found. Please log in again.");
            setIsLoading(false);
            return;
        }
        fetchData(userId);
    }, [fetchData]);

    // --- Modal Handlers (Keep existing) ---
    const openDocumentViewer = (url) => {
        if (!url) { alert("Document URL is not available or has expired."); return; }
        setDocViewerUrl(url); setIsDocViewerOpen(true);
    };
    const closeDocumentViewer = () => setIsDocViewerOpen(false);
    const openConsentModal = () => setIsConsentModalOpen(true);
    const closeConsentModal = () => setIsConsentModalOpen(false);

    // --- Input Change Handlers (Keep existing handleInputChange & handleArrayItemChange) ---
    const handleInputChange = (path, value) => {
        setFormData(prevData => {
            const newData = deepClone(prevData);
            if (!newData) {
                console.error("Failed to clone state in handleInputChange");
                setSaveError("Error updating field. Please try again.");
                return prevData;
            }
            const keys = path.split('.');
            let current = newData;
            try {
                for (let i = 0; i < keys.length - 1; i++) {
                    if (current[keys[i]] === undefined || current[keys[i]] === null) {
                        const nextKeyIsIndex = /^\d+$/.test(keys[i + 1]);
                        current[keys[i]] = nextKeyIsIndex ? [] : {};
                    }
                    current = current[keys[i]];
                }
                current[keys[keys.length - 1]] = value;
                return newData;
            } catch (e) {
                console.error("Error setting value in handleInputChange:", path, value, e);
                setSaveError(`Error updating field: ${path}. Please try again.`);
                return prevData;
            }
        });
    };

    const handleArrayItemChange = (arrayPath, index, fieldPath, value) => {
        setFormData(prevData => {
            const newData = deepClone(prevData);
            if (!newData) {
                console.error("Failed to clone state in handleArrayItemChange");
                setSaveError("Error updating item. Please try again.");
                return prevData;
            }
            try {
                // Navigate to the target array
                const arrayKeys = arrayPath.split('.');
                let currentArrayLevel = newData;
                for (let i = 0; i < arrayKeys.length; i++) {
                    if (currentArrayLevel[arrayKeys[i]] === undefined || currentArrayLevel[arrayKeys[i]] === null) {
                        console.warn(`Array path ${arrayPath} not found in handleArrayItemChange during navigation`);
                        // Attempt to create path if missing - necessary for nested arrays like kyc.buyers
                        if (i < arrayKeys.length - 1) {
                            currentArrayLevel[arrayKeys[i]] = {};
                        } else {
                            currentArrayLevel[arrayKeys[i]] = []; // Create the final array if it doesn't exist
                        }
                    }
                    currentArrayLevel = currentArrayLevel[arrayKeys[i]];
                }
                const targetArray = currentArrayLevel;


                if (!Array.isArray(targetArray)) {
                    console.warn(`Target path is not an array in handleArrayItemChange: path=${arrayPath}`);
                    // If it's supposed to be an array but isn't, potentially re-initialize? Or return error.
                    // For safety, let's log and return prevData
                    console.error(`Expected array at ${arrayPath} but found ${typeof targetArray}`);
                    return prevData;
                    // OR: Handle creation if absolutely necessary:
                    // if (newData path allows creation) newData[path] = []; targetArray = newData[path]; else return prevData;
                }

                // Ensure the item at index exists
                if (index < 0 || index >= targetArray.length) {
                    if (index === targetArray.length) {
                        // Allow adding a new item conceptually, but this handler is for *change*, not add.
                        // console.warn(`Index ${index} is out of bounds for array ${arrayPath}.`);
                        // For now, let's assume index is valid for existing items.
                        // Add item logic would be separate.
                        console.warn(`Invalid index in handleArrayItemChange: path=${arrayPath}, index=${index}`);
                        return prevData;
                    } else {
                        console.warn(`Invalid index in handleArrayItemChange: path=${arrayPath}, index=${index}`);
                        return prevData;
                    }
                }

                // Navigate within the item object
                const fieldKeys = fieldPath.split('.');
                let currentItemLevel = targetArray[index];
                if (!currentItemLevel) {
                    // This case should ideally be prevented by the index check above,
                    // but as a fallback, initialize the item if null/undefined.
                    targetArray[index] = {};
                    currentItemLevel = targetArray[index];
                    console.warn(`Item at index ${index} in ${arrayPath} was missing, initialized.`);
                }

                for (let i = 0; i < fieldKeys.length - 1; i++) {
                    if (currentItemLevel[fieldKeys[i]] === undefined || currentItemLevel[fieldKeys[i]] === null) {
                        currentItemLevel[fieldKeys[i]] = {}; // Create nested object path if needed
                    }
                    currentItemLevel = currentItemLevel[fieldKeys[i]];
                }
                currentItemLevel[fieldKeys[fieldKeys.length - 1]] = value;
                return newData;
            } catch (e) {
                console.error("Error setting array item value:", arrayPath, index, fieldPath, value, e);
                setSaveError(`Error updating item: ${arrayPath}[${index}].${fieldPath}. Please try again.`);
                return prevData;
            }
        });
    };

    const MAX_FILE_SIZE_MB = 20;
    const MAX_FILE_SIZE_BYTES = MAX_FILE_SIZE_MB * 1024 * 1024;
    const ACCEPTED_FORMATS = [
        'application/pdf',
        'application/vnd.ms-excel', // .xls
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // .xlsx
        'application/msword', // .doc
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document', // .docx
        'text/csv', // .csv
    ];
    const ACCEPTED_FORMATS_STRING = '.pdf, .jpg, jpeg, .png';
    const ACCEPTED_FORMATS_DISPLAY = 'PDF, JPEG, PNG, JPG';

    // --- Document Mapping (Keep existing) ---
    const documentMappings = {
        commercialRegistration: "commercialRegistration",
        tradeLicense: 'tradeLicense',
        taxCard: 'taxCard',
        establishmentCard: 'establishmentCard',
        memorandumOfAssociation: 'memorandumOfAssociation',
        articleOfAssociation: 'articleOfAssociation',
        commercialCreditReport: 'commercialCreditReport',
        bankStatement: "bankStatement",
        auditedFinancialReport: 'auditedFinancialReport',
        cashFlowLedger: 'cashFlowLedger',
        otherDocument: 'otherDocument',
        otherDocumentTwo: 'otherDocumentTwo',
        otherDocument3: 'otherDocument3',
        otherDocument4: 'otherDocument4',
        otherDocument5: 'otherDocument5',
        otherDocument6: 'otherDocument6',
        otherDocument7: 'otherDocument7',
        otherDocument8: 'otherDocument8',
        otherDocument9: 'otherDocument9',
        otherDocument10: 'otherDocument10',
        invoiceAdditionalDoc1: "invoiceAdditionalDoc1",
        invoiceAdditionalDoc2: "invoiceAdditionalDoc2",
        qatariId: "kyc.qatariId",
        passport: "kyc.passport",
        utilityBill: "kyc.utilityBill",
        employmentLetter: "kyc.employmentDetails.employmentLetter",
        proofOfIncome: "kyc.incomeDetails.proofOfIncome",
        shareholderPassport: 'shareholders.passport',
        shareholderQid: 'shareholders.qid',
        shareholderProofOfAddress: 'shareholders.proofOfAddress',
        // Note: Buyer documents are not typically uploaded via this general mechanism
        // They might have a dedicated flow or be handled differently.
        // The current backend preserves existing buyer documents, but doesn't upload new ones via this path.
    };

    // --- Document Upload/Replace Handler (Keep existing) ---
    const handleFileReplace = async (documentType, file, index = null, label = 'Document') => {
        if (!file) return;
        const userId = userData?._id || userData?.id;
        if (!userId) {
            setSaveError("User ID missing, cannot upload document.");
            return;
        }

        const uploadKey = index !== null ? `${documentType}-${index}` : documentType;
        const isShareholderDoc = documentType.startsWith('shareholder');

        setUploadingDoc(prev => ({ ...prev, [uploadKey]: true }));
        setIsSaving(true);
        setSaveError('');

        let finalUpdatedState = null;

        try {
            console.log(`Uploading document: userId=${userId}, type=${documentType}, index=${index}, file=${file.name}`);

            let response;
            if (isShareholderDoc) {
                const shareholderDocKey = documentType.replace('shareholder', '').toLowerCase(); // e.g., 'passport', 'qid'
                console.log(`Calling specific shareholder upload for key: ${shareholderDocKey}`);
                response = await uploadShareholderDocument(userId, index, shareholderDocKey, file);
            } else {
                // Check if it's a buyer document type - currently not handled by this flow
                if (documentType.startsWith('buyer')) {
                    console.error("Buyer document upload not implemented through this handler.");
                    setSaveError("Buyer document uploads need a specific implementation.");
                    throw new Error("Buyer document upload not supported here.");
                }
                response = await uploadKycDocument(userId, documentType, file);
            }

            if (response?.success && response?.documentData) {
                console.log("Upload successful:", response);
                const newDocData = ensureDocStructure({
                    filePath: response.documentData.filePath,
                    signedUrl: response.documentData.signedUrl,
                    uploadedOn: response.documentData.uploadedOn || new Date(),
                    mimeType: response.documentData.mimeType || file.type,
                });

                setFormData(prevData => {
                    const newData = deepClone(prevData);
                    if (!newData) {
                        console.error("Failed to clone state after upload.");
                        setSaveError("Failed to update UI after upload. Please save and reload.");
                        finalUpdatedState = null;
                        return prevData;
                    }

                    try {
                        if (isShareholderDoc) {
                            const actualDocType = documentType.replace('shareholder', '').toLowerCase();
                            if (newData.shareholders && newData.shareholders[index]) {
                                newData.shareholders[index] = newData.shareholders[index] || {};
                                newData.shareholders[index][actualDocType] = newDocData;
                                console.log(`Updated shareholder doc in state: shareholders[${index}].${actualDocType}`);
                            } else {
                                console.error(`Shareholder index ${index} not found in formData during update.`);
                                finalUpdatedState = null;
                                return prevData;
                            }
                        } else {
                            const path = documentMappings[documentType];
                            if (path) {
                                const keys = path.split('.');
                                let current = newData;
                                for (let i = 0; i < keys.length - 1; i++) {
                                    if (current[keys[i]] === undefined || current[keys[i]] === null) {
                                        current[keys[i]] = {};
                                    }
                                    current = current[keys[i]];
                                }
                                current[keys[keys.length - 1]] = newDocData;
                                console.log(`Updated doc in state at path: ${path}`);
                            } else {
                                console.error(`Document mapping not found for type: ${documentType}`);
                                finalUpdatedState = null;
                                return prevData;
                            }
                        }
                        finalUpdatedState = newData;
                        return newData;
                    } catch (e) {
                        console.error("Error placing uploaded doc data into state:", e);
                        setSaveError("Error updating UI after upload. Please save and reload.");
                        finalUpdatedState = null;
                        return prevData;
                    }
                });

                if (finalUpdatedState) {
                    const clonedFinalState = deepClone(finalUpdatedState);
                    if (clonedFinalState) {
                        // IMPORTANT: Update userData as well so the non-edit view reflects the change immediately
                        setUserData(clonedFinalState);
                        console.log("Updated userData state after successful document upload.");
                    } else {
                        console.warn("Failed to clone final state for userData update after upload.");
                    }
                }

            } else {
                throw new Error(response?.error || response?.message || "Upload failed or returned invalid data.");
            }
        } catch (error) {
            console.error(`Error uploading document (${documentType}, index ${index}):`, error);
            setSaveError(`Error uploading ${label}: ${error.message}`);
        } finally {
            setUploadingDoc(prev => ({ ...prev, [uploadKey]: false }));
            setIsSaving(false);
            if (fileInputRefs.current[uploadKey]) {
                fileInputRefs.current[uploadKey].value = null;
            }
        }
    };


    // --- Trigger hidden file input (Keep existing) ---
    const triggerFileInput = (key) => {
        if (fileInputRefs.current[key]) {
            fileInputRefs.current[key].click();
        } else {
            console.warn(`File input ref not found for key: ${key}`);
        }
    };

    // --- Add this useEffect hook (Resolved Merge Conflict - Kept HEAD version) ---
    useEffect(() => {
        if (!userData) {
            setMissingFieldLabels([]); // Clear if no user data
            return;
        }

        // Define required fields check list with section information
        const requiredFieldsToCheck = [
            { path: "kyc.businessDetails.businessName", label: "Trade Name / Legal Entity Name", section: "Business Details" },
            { path: "kyc.businessDetails.crNumber", label: "CR Number", section: "Business Details" },
            { path: "kyc.businessDetails.crIssueDate", label: "CR Issue Date", section: "Business Details" },
            { path: "kyc.businessDetails.crExpiryDate", label: "CR Expiry Date", section: "Business Details" },
            { path: "kyc.businessDetails.taxRegNo", label: "Tax Reg. No.", section: "Business Details" },
            { path: "firstName", label: "First Name", section: "Personal Details" },
            { path: "lastName", label: "Last Name", section: "Personal Details" },
            { path: "email", label: "Business Email ID", section: "Business Details" },
            { path: "mobileNo", label: "Contact Number", section: "Personal Details" },
            // QID fields commented out as per requirements
            // { path: "kyc.qidNumber", label: "QID Number", section: "Personal Details" },
            // { path: "kyc.qidExpiryDate", label: "QID Expiry Date", section: "Personal Details" },
            // { path: "kyc.nationality", label: "Nationality", section: "Personal Details" },
            { path: "kyc.incomeDetails.accountNumber", label: "Account Number", section: "Bank Account Details" },
            { path: "kyc.incomeDetails.ifscCode", label: "IBAN", section: "Bank Account Details" }
            // Add any other fields that YOU consider mandatory here
        ];

        const missingLabels = [];

        // Check basic required fields from the list above
        requiredFieldsToCheck.forEach(field => {
            const value = getNestedValue(userData, field.path, '');
            if (!hasValue(value)) { // hasValue is your existing helper function
                missingLabels.push(`${field.label} of ${field.section}`);
            }
        });

        // Check mandatory buyer fields (name and email)
        const buyers = userData?.kyc?.buyers || [];
        if (buyers.length > 0) { // Only check if buyers exist
            buyers.forEach((buyer, index) => {
                if (!hasValue(buyer.buyerName)) {
                    missingLabels.push(`Buyer ${index + 1}: Name of Buyer Details`);
                }
                if (!hasValue(buyer.contactEmail)) {
                    missingLabels.push(`Buyer ${index + 1}: Contact Email of Buyer Details`);
                }
            });
        }
        // else {
        // If having at least one buyer is mandatory, uncomment this:
        // missingLabels.push("At least one Buyer's details");
        // }

        // Update state with the list of missing field labels (ensure uniqueness)
        setMissingFieldLabels([...new Set(missingLabels)]);

    }, [userData]); // Re-run check whenever userData changes
    // --- End of added useEffect hook ---


    // Helper function to check if a field has a value
    const hasValue = (value) => {
        return value !== null && value !== undefined && String(value).trim() !== '';
    };

    // Function to check if all mandatory buyer fields are filled
    const checkMandatoryBuyerFields = () => {
        if (!userData?.kyc?.buyers || userData.kyc.buyers.length === 0) {
            return true; // No buyers to check
        }

        for (let i = 0; i < userData.kyc.buyers.length; i++) {
            const buyer = userData.kyc.buyers[i];
            // Check only the two mandatory fields: buyerName and contactEmail
            if (!hasValue(buyer.buyerName) || !hasValue(buyer.contactEmail)) {
                return false;
            }
        }
        return true;
    };

    // --- Final Submission Handler (Updated to check mandatory buyer fields) ---
    const handleSubmit = async (e) => {
        e.preventDefault();
        setSubmitError('');
        if (editingSectionKey !== null) { // Check if any section is currently being edited
            setSubmitError('Please save or cancel your edits in the active section before submitting.'); return;
        }
        if (!agreedAccurate || !readTerms || !readFinancialConsent) {
            setSubmitError('Please review and agree to all consents and declarations.'); return;
        }
        if (!userData) { setSubmitError("User data is missing."); return; }

        // Check if all mandatory buyer fields are filled
        if (!checkMandatoryBuyerFields()) {
            setSubmitError('Please fill in all mandatory buyer fields (Name and Contact Email) before submitting.');
            return;
        }

        setIsSubmitting(true);
        const userId = userData._id || userData.id;
        const kycStatusUpdatePayload = { id: userId, kyc: { verificationStatus: "UNDER_REVIEW" } };

        const API_BASE_URL = config.apiUrl || "https://madadapi.fundfina.com";
        const CREDIT_LINE_ENDPOINT = `${API_BASE_URL}/ops/invoiceFinancing/creditLineCreateOrUpdate`;

        try {
            console.log("Submitting KYC status update:", kycStatusUpdatePayload);
            const kycUpdateResult = await updateKyc(kycStatusUpdatePayload);

            if (!kycUpdateResult?.success) {
                throw new Error(kycUpdateResult?.message || "Failed to update KYC status.");
            }
            console.log("KYC Status updated.");

            const updatedUserData = kycUpdateResult.user || { ...userData, kyc: { ...(userData.kyc || {}), verificationStatus: "UNDER_REVIEW" } };
            const clonedUpdatedData = deepClone(updatedUserData);

            if (clonedUpdatedData) {
                setUserData(clonedUpdatedData);
                setFormData(deepClone(clonedUpdatedData)); // Sync form data too
            } else {
                console.error("Failed to clone updated user data after status update.");
            }

            console.log("Submitting initial Credit Line request...");
            const creditLinePayload = { userId: userId, creditLineData: { creditLimit: 0, tenure: 0, interestRate: 0, processingFee: 0, creditLineStatus: "UNDER_REVIEW", creditLineAssignedBy: "lenderAdmin" }, reviewedBy: userId };
            const creditLineResponse = await fetch(CREDIT_LINE_ENDPOINT, { method: 'POST', headers: { 'Content-Type': 'application/json' }, body: JSON.stringify(creditLinePayload) });
            const creditLineResult = await creditLineResponse.json();
            if (!creditLineResponse.ok || !creditLineResult.success) {
                console.warn("Failed to create/update initial credit line:", creditLineResult);
                // Don't necessarily stop submission, maybe just log it
            } else {
                console.log("Initial credit line request submitted.");
            }

            history.push('/dashboard', { initialTab: 'journey' });
        } catch (error) {
            console.error('Error during final submission:', error);
            setSubmitError(error.message || 'An unexpected error occurred during submission.');
        } finally {
            setIsSubmitting(false);
        }
    };


    // --- Render Helper: Detail Item (View/Edit) (Modified to highlight missing fields and validate expiry dates) ---
    const renderEditableDetailItem = (label, value, path, inputType = 'text', options = null, isRequired = false, isEditingThisSection = false) => {
        const isEmpty = !hasValue(value);

        // Check if this is an expiry date field that needs validation
        const isExpiryField = (
            path === 'kyc.businessDetails.crExpiryDate' ||
            path === 'kyc.businessDetails.establishmentIdExpiryDate' ||
            path === 'kyc.businessDetails.tlExpiryDate'
        );

        // Validate expiry date if it's an expiry field and has a value
        const isDateInvalid = isExpiryField && !isEmpty && !isExpiryDateValid(value);

        const displayValue = isEmpty
            ? <span className={`italic ${isRequired ? 'text-red-500' : 'text-gray-400'}`}>
                {isRequired ? 'Required - Not Provided' : 'Not Provided'}
            </span>
            : String(value);

        const formValue = getNestedValue(formData, path, '');

        const inputProps = {
            id: path,
            name: path,
            value: inputType === 'date' ? formatDateForInput(formValue) : formValue,
            onChange: (e) => {
                const targetValue = inputType === 'checkbox' ? e.target.checked : e.target.value;
                handleInputChange(path, targetValue);
            },
            className: `block w-full rounded-md shadow-sm border focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm py-1.5 px-2 ${isRequired && isEmpty ? 'border-red-300 bg-red-50' : 'border-gray-300'}`,
        };
        const checkboxClass = "h-4 w-4 text-black-600 border-gray-300 rounded focus:ring-black-500";

        return (
            <div className={`py-2 px-1 sm:grid sm:grid-cols-3 sm:gap-4 border-b border-gray-100 last:border-b-0 items-center ${isRequired && isEmpty ? 'bg-red-50' : ''}`}>
                <dt className={`text-sm font-medium whitespace-normal break-words ${isRequired && isEmpty ? 'text-red-500' : 'text-gray-500'}`}>
                    {label} {isRequired && <span className="text-red-500">*</span>}
                </dt>
                <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2 break-words">
                    {isEditingThisSection ? (
                        inputType === 'select' && options ? (
                            <select {...inputProps}>
                                <option value="">Select...</option>
                                {options.map(opt => <option key={opt.value} value={opt.value}>{opt.label}</option>)}
                            </select>
                        ) : inputType === 'date' ? (
                            <input type="date" {...inputProps} />
                        ) : inputType === 'checkbox' ? (
                            <input type="checkbox" id={path} name={path} checked={!!formValue} onChange={inputProps.onChange} className={checkboxClass} />
                        ) : inputType === 'number' ? (
                            <input type="number" {...inputProps} />
                        ) : (
                            <input type={inputType} {...inputProps} />
                        )
                    ) : (
                        inputType === 'date' ? (
                            <span className={isDateInvalid ? 'text-red-500 font-medium' : ''}>
                                {formatDate(value)}
                            </span>
                        ) : inputType === 'checkbox' ? (value ? 'Yes' : 'No')
                            : displayValue
                    )}
                </dd>
            </div>
        );
    };

    // --- Render Helper: Address (View/Edit) (Modified to highlight missing fields) ---
    const renderEditableAddress = (_, basePath, title = "Address", isBuyer = false, isRequired = false, isEditingThisSection = false) => {
        let entityType = 'user';
        if (basePath.includes('businessDetails')) entityType = 'business';
        else if (basePath.startsWith('shareholders.')) entityType = 'shareholder';
        else if (isBuyer) entityType = 'buyer';

        const userAddressFields = [
            { label: "Address Line 1", key: "addressLine1", path: `${basePath}.addressLine1`, required: isRequired },
            { label: "Address Line 2", key: "addressLine2", path: `${basePath}.addressLine2`, required: false },
            { label: "City", key: "city", path: `${basePath}.city`, required: isRequired },
            { label: "Country", key: "country", path: `${basePath}.country`, required: isRequired }
        ];
        const businessAddressFields = [
            { label: "Address Line 1", key: "businessAddressLine1", path: `${basePath}.businessAddressLine1`, required: isRequired },
            { label: "Address Line 2", key: "businessAddressLine2", path: `${basePath}.businessAddressLine2`, required: false },
            { label: "City", key: "businessCity", path: `${basePath}.businessCity`, required: isRequired },
            { label: "Country", key: "businessCountry", path: `${basePath}.businessCountry`, required: isRequired }
        ];
        const shareholderAddressFields = [
            { label: "Zone", key: "zone", path: `${basePath}.zone`, required: isRequired },
            { label: "Street No.", key: "streetNo", path: `${basePath}.streetNo`, required: isRequired },
            { label: "Building No.", key: "buildingNo", path: `${basePath}.buildingNo`, required: isRequired },
            { label: "Floor No.", key: "floorNo", path: `${basePath}.floorNo`, required: isRequired },
            { label: "Unit No. / Flat No.", key: "unitNo", path: `${basePath}.unitNo`, required: isRequired }
        ];
        const buyerAddressFields = [
            { label: "Buyer Address", key: "buyerAddress", path: `${basePath}.buyerAddress`, required: isRequired }
        ];

        let fields = entityType === 'business' ? businessAddressFields
            : entityType === 'shareholder' ? shareholderAddressFields
                : entityType === 'buyer' ? buyerAddressFields
                    : userAddressFields;

        let arrayName = null;
        let itemIndex = null;
        let itemBasePathForRelativeCalc = null;

        if (basePath.startsWith('shareholders.')) {
            const match = basePath.match(/shareholders\.(\d+)/);
            if (match) {
                arrayName = 'shareholders';
                itemIndex = parseInt(match[1], 10);
                itemBasePathForRelativeCalc = `${arrayName}.${itemIndex}.`;
            }
        } else if (basePath.startsWith('kyc.buyers.')) {
            const match = basePath.match(/kyc\.buyers\.(\d+)/);
            if (match) {
                arrayName = 'kyc.buyers';
                itemIndex = parseInt(match[1], 10);
                itemBasePathForRelativeCalc = `${arrayName}.${itemIndex}.`;
            }
        }

        const currentAddressForEdit = getNestedValue(formData, basePath, {});
        const originalAddressForView = getNestedValue(userData, basePath, {});
        const hasOriginalData = originalAddressForView && fields.some(f => {
            const keyParts = f.key.split('.');
            let val = originalAddressForView;
            for (const part of keyParts) { val = val?.[part]; if (val === undefined || val === null) break; }
            return val !== undefined && val !== null && String(val).trim() !== '';
        });

        // Check if any required fields are missing
        const hasMissingRequiredFields = fields.some(field => {
            if (!field.required) return false;
            const value = getNestedValue(originalAddressForView, field.key, '');
            return !hasValue(value);
        });

        return (
            <div className="py-2 px-0">
                <dt className={`text-sm font-medium mb-1 ${hasMissingRequiredFields && isRequired ? 'text-red-500' : 'text-gray-500'}`}>
                    {title} {isRequired && <span className="text-red-500">*</span>}
                </dt>
                <dd className={`mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2 pl-4 border-l-2 ml-1 space-y-1 ${hasMissingRequiredFields && isRequired ? 'border-red-300' : 'border-gray-200'}`}>
                    {(isEditingThisSection || hasOriginalData) ? (
                        fields.map(field => {
                            const originalValue = getNestedValue(originalAddressForView, field.key, '');
                            const shouldRenderView = !isEditingThisSection && (originalValue !== null && originalValue !== undefined && String(originalValue).trim() !== '');
                            const shouldRenderEdit = isEditingThisSection;
                            if (!shouldRenderView && !shouldRenderEdit) return null;

                            const editValue = getNestedValue(currentAddressForEdit, field.key, '');
                            const isEmpty = !hasValue(originalValue);
                            const isFieldRequired = field.required;

                            return (
                                <div key={field.path} className={`py-1 px-1 sm:grid sm:grid-cols-3 sm:gap-2 border-b border-gray-100 last:border-b-0 items-center ${isFieldRequired && isEmpty ? 'bg-red-50' : ''}`}>
                                    <dt className={`text-xs font-medium ${isFieldRequired && isEmpty ? 'text-red-500' : 'text-gray-500'}`}>
                                        {field.label} {isFieldRequired && <span className="text-red-500">*</span>}
                                    </dt>
                                    <dd className="mt-1 text-xs text-gray-900 sm:mt-0 sm:col-span-2">
                                        {isEditingThisSection ? (
                                            <input
                                                type="text"
                                                id={field.path}
                                                name={field.path}
                                                value={editValue}
                                                onChange={(e) => {
                                                    if (arrayName !== null && itemIndex !== null && itemBasePathForRelativeCalc) {
                                                        // Calculate path relative to the item *within* the array
                                                        const fieldPathWithinItem = field.path.startsWith(itemBasePathForRelativeCalc)
                                                            ? field.path.substring(itemBasePathForRelativeCalc.length)
                                                            : null;

                                                        if (fieldPathWithinItem) {
                                                            handleArrayItemChange(arrayName, itemIndex, fieldPathWithinItem, e.target.value);
                                                        } else {
                                                            console.error("Error: Could not determine relative field path for array item change.", {
                                                                fullPath: field.path,
                                                                itemBase: itemBasePathForRelativeCalc,
                                                            });
                                                            setSaveError(`Internal error updating field ${field.label}. Please try again.`);
                                                        }
                                                    } else {
                                                        // If not part of an array, use handleInputChange with the full path
                                                        handleInputChange(field.path, e.target.value);
                                                    }
                                                }}
                                                className={`block w-full rounded-md shadow-sm border focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 sm:text-xs py-1 px-1.5 ${isFieldRequired && isEmpty ? 'border-red-300 bg-red-50' : 'border-gray-300'}`} />
                                        ) : (
                                            (originalValue !== null && originalValue !== undefined && String(originalValue).trim() !== '')
                                                ? String(originalValue)
                                                : <span className={`italic text-xs ${isFieldRequired ? 'text-red-500' : 'text-gray-400'}`}>
                                                    {isFieldRequired ? 'Required - Not Provided' : 'Not Provided'}
                                                </span>
                                        )}
                                    </dd>
                                </div>
                            );
                        })
                    ) : (
                        <div className={`py-1 px-0 text-xs italic ${isRequired ? 'text-red-500' : 'text-gray-400'}`}>
                            {isRequired ? 'Required - Not Provided' : 'Not Provided'}
                        </div>
                    )}
                </dd>
            </div>
        );
    };


    const renderDocument = (docData, label, documentType, index = null, isRequired = false, isEditingThisSection = false) => {
        console.log(docData.verificationStatus, "DOC DATS");
        const currentDoc = ensureDocStructure(docData);
        const uploadKey = index !== null ? `${documentType}-${index}` : documentType;
        const isUploading = uploadingDoc[uploadKey];
        const hasDoc = !!currentDoc.filePath;
        const fullFileName = hasDoc ? getFileNameFromPath(currentDoc.filePath) : 'Not Uploaded';
        const fileName = hasDoc ? shortenDocumentName(fullFileName, 15) : 'Not Uploaded'; // Shorten filename for display
        const viewUrl = currentDoc.signedUrl;

        // Disable upload/replace for unsupported types or if not editing
        const canUpload = isEditingThisSection && !isUploading && (documentMappings[documentType] || documentType.startsWith('shareholder'));

        // Determine the overall state for rendering
        const isUploaded = hasDoc && !isUploading;
        const isError = !hasDoc && isRequired && !canUpload; // Simplified error state for required but not uploaded/supported

        // --- Helper Function for Status Badge with Icons ---
        const renderVerificationStatusBadge = (status) => {
            if (!status || !isUploaded) { // Only render if document is uploaded and has a status
                return null;
            }

            let bgColor = 'bg-blue-100'; // Default blue for Submitted
            let textColor = 'text-blue-700'; // Default blue text
            let IconComponent = InformationCircleIcon; // Default icon
            let text = 'Submitted'; // Default text

            switch (status) {
                case 'PENDING':
                    bgColor = 'bg-yellow-100';
                    textColor = 'text-yellow-700';
                    IconComponent = ClockIcon;
                    text = 'Pending';
                    break;
                case 'VERIFIED':
                    bgColor = 'bg-green-100';
                    textColor = 'text-green-700';
                    IconComponent = CheckCircleIcon;
                    text = 'Verified';
                    break;
                case 'REJECTED':
                    bgColor = 'bg-red-100';
                    textColor = 'text-red-700';
                    IconComponent = XCircleIcon;
                    text = 'Rejected';
                    break;
                case 'SKIPPED':
                    bgColor = 'bg-gray-100';
                    textColor = 'text-gray-700';
                    IconComponent = InformationCircleIcon;
                    text = 'Skipped';
                    break;
                case 'SUBMITTED':
                default:
                    break; // Keep defaults (Blue)
            }

            return (
                <div className="z-10">
                    <span className={`inline-flex items-center gap-1 px-2 py-0.5 rounded-full text-xs font-medium ${bgColor} ${textColor}`}>
                        <IconComponent className="h-4 w-4" />
                        {text}
                    </span>
                </div>
            );
        };
        // --- End Helper ---

        return (
            <div className="relative border border-gray-200 rounded-lg p-3 bg-white shadow-sm flex flex-col justify-between w-full max-w-full overflow-hidden">
                <div className="flex-1">
                    <div className="flex flex-wrap items-center justify-between mb-1 gap-2">
                        <label className="text-sm font-medium text-gray-700">
                            {label} {isRequired && <span className="text-red-500">*</span>}
                        </label>
                        {isUploaded && (
                            <span className="text-sm text-green-600 font-semibold flex items-center shrink-0">
                                <CheckIcon className="h-4 w-4 mr-1" /> Uploaded
                            </span>
                        )}
                    </div>

                    {isUploading ? (
                        <div className="text-sm text-blue-600 font-semibold flex flex-col items-center text-center gap-1">
                            {/* Assuming you have a LoadingSpinner component or equivalent */}
                            <svg className="animate-spin h-6 w-6 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                            <span>Uploading...</span>
                            {fullFileName && (
                                <span className="text-xs text-gray-500 truncate max-w-full break-all" title={fullFileName}>
                                    {fileName}
                                </span>
                            )}
                        </div>
                    ) : isUploaded ? (
                        <div>
                            <div className="bg-[#eff7f7] border-2 border-dashed border-gray-300 rounded-md p-3 text-center overflow-hidden">
                                <span
                                    className="text-sm font-medium text-gray-700 truncate break-words block"
                                    title={fullFileName}
                                >
                                    {fileName}
                                </span>
                            </div>
                            {/* You can add an error state here if renderDocument needs to handle internal validation errors, similar to FileUploadField */}
                            {/* {currentDoc.error && ( 
                            <p className="mt-1 text-xs text-orange-500 text-center break-words">
                                Note: {currentDoc.error}
                            </p>
                        )} */}
                        </div>
                    ) : ( // Not uploaded or not supported (similar to the initial upload button)
                        <div>
                            <button
                                type="button"
                                onClick={() => canUpload && triggerFileInput(uploadKey)}
                                className={`bg-[#eff7f7] w-full flex flex-col items-center justify-center px-3 py-2 border-2 border-dashed rounded-md text-sm font-medium focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#004141] group
                                ${!canUpload ? 'border-gray-300 bg-gray-100 text-gray-400 cursor-not-allowed'
                                        : isError ? 'border-red-400 text-red-600 hover:bg-red-50' // If required and not uploaded/supported
                                            : 'border-gray-300 text-gray-500 hover:border-gray-400 hover:bg-gray-100'}`}
                                disabled={!canUpload}
                            >
                                <div className="flex items-center space-x-2">
                                    <ArrowUpTrayIcon className="h-5 w-5" /> {/* Using UploadIcon equivalent */}
                                    <span>{isRequired && !canUpload ? 'Required - Not Uploaded' : 'Upload'}</span>
                                </div>
                                {/* Assuming MAX_FILE_SIZE_MB and ACCEPTED_FORMATS_DISPLAY are accessible */}
                                <p className="text-xs text-gray-500 text-center">Max filesize {MAX_FILE_SIZE_MB || 'X'}MB</p>
                            </button>

                            <p className="mt-1 text-xs text-center text-gray-500">Formats: {ACCEPTED_FORMATS_DISPLAY || '.pdf, .jpg, .png'}</p>
                            {isError && (
                                <p className="mt-1 text-xs text-red-500 text-center break-words">
                                    This document is required and has not been uploaded or is not supported for upload in this section.
                                </p>
                            )}
                        </div>
                    )}
                </div>

                {isUploaded && (
                    <div className="mt-3 flex flex-wrap items-center justify-between gap-y-2">
                        <div className="flex gap-4 flex-wrap">
                            {viewUrl && (
                                <button
                                    type="button"
                                    onClick={() => openDocumentViewer(viewUrl)} // Assuming openDocumentViewer is defined
                                    className="flex items-center text-xs text-black hover:underline font-medium"
                                    title="View Document"
                                >
                                    <EyeIcon className="h-4 w-4 mr-1" /> View
                                </button>
                            )}
                            {canUpload && ( // Only allow replace if upload is enabled for this section
                                <button
                                    type="button"
                                    onClick={() => triggerFileInput(uploadKey)}
                                    className="flex items-center text-xs text-black hover:underline font-medium"
                                    title="Replace Document"
                                >
                                    <ArrowPathIcon className="h-4 w-4 mr-1" /> Replace
                                </button>
                            )}
                        </div>
                        <div className="shrink-0">{renderVerificationStatusBadge(currentDoc.verificationStatus)}</div>
                    </div>
                )}

                <input
                    type="file"
                    accept=".pdf,.jpg,.jpeg,.png" // Keep this specific for renderDocument's use case
                    ref={ref => fileInputRefs.current[uploadKey] = ref}
                    onChange={(e) => handleFileReplace(documentType, e.target.files?.[0], index, label)}
                    className="hidden"
                    disabled={!canUpload}
                />
            </div>
        );
    };

    // --- Render Helper: Single Buyer Item (Modified to highlight missing fields) ---
    const renderEditableBuyerItem = (label, value, arrayPath, index, fieldPath, inputType = 'text', isRequired = false, isEditingThisSection = false) => {
        const isEmpty = !hasValue(value);
        const displayValue = isEmpty
            ? <span className={`italic ${isRequired ? 'text-red-500' : 'text-gray-400'}`}>
                {isRequired ? 'Required - Not Provided' : 'Not Provided'}
            </span>
            : String(value);

        const formValue = getNestedValue(formData, `${arrayPath}.${index}.${fieldPath}`, '');

        const inputProps = {
            id: `${arrayPath}-${index}-${fieldPath}`, // Unique ID
            name: `${arrayPath}-${index}-${fieldPath}`,
            value: inputType === 'date' ? formatDateForInput(formValue) : formValue, // Handle date if needed later
            onChange: (e) => {
                const targetValue = inputType === 'checkbox' ? e.target.checked : e.target.value;
                handleArrayItemChange(arrayPath, index, fieldPath, targetValue);
            },
            className: `block w-full rounded-md shadow-sm border focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm py-1.5 px-2 ${isRequired && isEmpty ? 'border-red-300 bg-red-50' : 'border-gray-300'}`,
        };
        // Add checkbox handling if needed later
        // const checkboxClass = "h-4 w-4 text-black-600 border-gray-300 rounded focus:ring-black-500";

        return (
            <div className={`py-2 px-1 sm:grid sm:grid-cols-3 sm:gap-4 border-b border-gray-100 last:border-b-0 items-center ${isRequired && isEmpty ? 'bg-red-50' : ''}`}>
                <dt className={`text-sm font-medium whitespace-normal break-words ${isRequired && isEmpty ? 'text-red-500' : 'text-gray-500'}`}>
                    {label} {isRequired && <span className="text-red-500">*</span>}
                </dt>
                <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2 break-words">
                    {isEditingThisSection ? (
                        inputType === 'number' ? (
                            <input type="number" {...inputProps} />
                        ) : ( // Default to text
                            <input type={inputType} {...inputProps} />
                        )
                    ) : (
                        displayValue // Display logic for non-editing mode
                    )}
                </dd>
            </div>
        );
    };


    // Loading/Error/No Data States (Keep existing)
    if (isLoading) {
        return <LoadingPopup />;
    }
    if (loadError) return <div className="max-w-4xl mx-auto p-6 my-10 border border-red-400 bg-red-50 text-red-700 rounded-md text-center"><ExclamationTriangleIcon className="h-6 w-6 inline mr-2" />{loadError} <button onClick={() => window.location.reload()} className="ml-2 underline">Reload</button></div>;
    if (!userData || !formData) return <div className="text-center p-10 text-gray-500">No user data available to review. <button onClick={() => window.location.reload()} className="ml-2 underline">Reload</button></div>;

    // Safe Access
    const kyc = formData.kyc || {};
    const shareholders = formData.shareholders || [];
    const buyers = formData.kyc?.buyers || []; // Use formData for current state
    const directors = kyc.directors || [];

    // Define required fields for counting
    const requiredFields = [
        { path: "kyc.businessDetails.businessName", label: "Trade Name / Legal Entity Name" },
        { path: "kyc.businessDetails.crNumber", label: "CR Number" },
        { path: "kyc.businessDetails.crIssueDate", label: "CR Issue Date" },
        { path: "kyc.businessDetails.crExpiryDate", label: "CR Expiry Date" },
        { path: "kyc.businessDetails.taxRegNo", label: "Tax Reg. No." },
        { path: "firstName", label: "First Name" },
        { path: "lastName", label: "Last Name" },
        { path: "email", label: "Business Email ID" },
        { path: "mobileNo", label: "Contact Number" },
        // QID fields commented out as per requirements
        // { path: "kyc.qidNumber", label: "QID Number" },
        // { path: "kyc.qidExpiryDate", label: "QID Expiry Date" },
        // { path: "kyc.nationality", label: "Nationality" },
        { path: "kyc.incomeDetails.accountNumber", label: "Account Number" },
        { path: "kyc.incomeDetails.ifscCode", label: "IBAN" }
    ];

    // Count completed required fields
    const countCompletedFields = () => {
        let completed = 0;
        let total = requiredFields.length;

        requiredFields.forEach(field => {
            const value = getNestedValue(userData, field.path, '');
            if (hasValue(value)) {
                completed++;
            }
        });

        // Count mandatory buyer fields
        if (userData?.kyc?.buyers && userData.kyc.buyers.length > 0) {
            userData.kyc.buyers.forEach((buyer) => {
                // Add 2 to total for each buyer (name and contact email)
                total += 2;

                // Check if buyer name is filled
                if (hasValue(buyer.buyerName)) {
                    completed++;
                }

                // Check if contact email is filled
                if (hasValue(buyer.contactEmail)) {
                    completed++;
                }
            });
        }

        // No required documents anymore
        // Keeping the structure but not counting any documents as required

        return { completed, total };
    };

    const { completed, total } = countCompletedFields();



    // --- Component Render ---
    return (
        <div className="bg-white min-h-screen border border-gray-100 rounded-lg">
            <div className="bg-[#f0f0f0] shadow-lg rounded-lg"> {/* Container for title and content */}
                <div className="px-6 bg-white py-4 border-b border-gray-200">
                    <h1 className="text-2xl font-bold text-gray-800"> {/* Adjusted title style */}
                        Review Your Information
                    </h1>
                    <p className="mt-1 text-sm text-gray-600">
                        Please carefully review all the information provided. Use the ‘Edit Information’ Button to make changes if necessary.                    </p>
                </div>
                {/* Completion Status */}
                <div className={`mt-4 mx-6 p-4 rounded-md shadow ${missingFieldLabels.length > 0 ? 'bg-red-50 border border-red-200' : 'bg-green-50 border border-green-200'}`}>
                    <div className="flex items-center justify-between mb-1">
                        <h3 className={`text-md font-medium ${missingFieldLabels.length > 0 ? 'text-red-700' : 'text-green-700'}`}>
                            {missingFieldLabels.length > 0 ? 'Missing Required Information' : 'All Required Information Appears To Be Provided'}
                        </h3>
                        {missingFieldLabels.length > 0 && (
                            <span className={`text-sm font-medium text-red-700`}>
                                {missingFieldLabels.length} field(s) potentially missing
                            </span>
                        )}
                    </div>
                    {missingFieldLabels.length > 0 && (
                        <div className="mt-2 text-sm text-red-600">
                            <p className="font-medium mb-1 text-xs">Please ensure the following are complete before final submission:</p>
                            <ul className="list-disc pl-5 space-y-0.5 text-xs">
                                {missingFieldLabels.map((fieldLabel, index) => (
                                    <li key={index}>{fieldLabel}</li>
                                ))}
                            </ul>
                        </div>
                    )}
                </div>

                {/* Save Error Display (for section saves) */}
                {saveError && <p className="text-red-600 text-center mb-4 text-sm p-3 mx-6 mt-4 bg-red-100 border border-red-300 rounded-md shadow">{saveError}</p>}

                {/* --- Form Wrapper --- */}
                <form onSubmit={handleSubmit} className="space-y-6 p-6">

                    {/* 1. Business Details Section */}
                    <div className="bg-white shadow rounded-lg">
                        <div
                            className="px-4 py-4 sm:px-6 border-b border-gray-200 flex justify-between items-center cursor-pointer"
                            onClick={() => toggleSectionExpand('businessDetails')}
                        >
                            <h3 className="text-lg leading-6 font-medium text-gray-900">Business Details</h3>
                            <div className="flex space-x-2 items-center">
                                {editingSectionKey === 'businessDetails' ? (
                                    <div className="flex space-x-2">
                                        <button
                                            type="button"
                                            onClick={(e) => { e.stopPropagation(); handleSaveSection('businessDetails'); }}
                                            disabled={isSaving && currentSavingSection === 'businessDetails'}
                                            className="px-3 py-1.5 text-xs font-medium text-white bg-green-600 rounded-md hover:bg-green-700 disabled:bg-gray-400 flex items-center"
                                        >
                                            {(isSaving && currentSavingSection === 'businessDetails') ? <ArrowPathIcon className="h-4 w-4 mr-1 animate-spin" /> : <CheckIcon className="h-4 w-4 mr-1" />}
                                            Save
                                        </button>
                                        <button
                                            type="button"
                                            onClick={(e) => { e.stopPropagation(); handleCancelSectionEdit('businessDetails'); }}
                                            className="px-3 py-1.5 text-xs font-medium text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300 flex items-center"
                                        >
                                            <XMarkIcon className="h-4 w-4 mr-1" /> Cancel
                                        </button>
                                    </div>
                                ) : (
                                    <button
                                        type="button"
                                        onClick={(e) => { e.stopPropagation(); handleEditSectionToggle('businessDetails'); }}
                                        className="px-3 py-1.5 bg-gray-200 text-xs font-medium text-black-600 hover:text-black-800 rounded-md border border-black-600 hover:bg-black-50 flex items-center"
                                    >
                                        <PencilSquareIcon className="h-4 w-4 mr-1" /> Edit Information
                                    </button>
                                )}
                                {expandedSections.businessDetails ? <ChevronUpIcon className="h-5 w-5 text-gray-500" /> : <ChevronDownIcon className="h-5 w-5 text-gray-500" />}
                            </div>
                        </div>
                        <div
                            className={`transition-all duration-300 ease-in-out overflow-hidden ${expandedSections.businessDetails ? 'max-h-screen opacity-100' : 'max-h-0 opacity-0'}`}
                        >
                            <div className="px-4 py-5 sm:p-6">
                                <dl className="divide-y divide-gray-200">
                                    {renderEditableDetailItem("Trade Name / Legal Entity Name", getNestedValue(userData, "kyc.businessDetails.businessName"), "kyc.businessDetails.businessName", "text", null, true, editingSectionKey === 'businessDetails')}
                                    {renderEditableDetailItem("CR Number", getNestedValue(userData, "kyc.businessDetails.crNumber"), "kyc.businessDetails.crNumber", "text", null, true, editingSectionKey === 'businessDetails')}
                                    {renderEditableDetailItem("Legal Form", getNestedValue(userData, "kyc.businessDetails.legalForm"), "kyc.businessDetails.legalForm", "text", null, false, editingSectionKey === 'businessDetails')}
                                    {renderEditableDetailItem("CR Issue Date", getNestedValue(userData, "kyc.businessDetails.crIssueDate"), "kyc.businessDetails.crIssueDate", "date", null, true, editingSectionKey === 'businessDetails')}
                                    {renderEditableDetailItem("CR Expiry Date", getNestedValue(userData, "kyc.businessDetails.crExpiryDate"), "kyc.businessDetails.crExpiryDate", "date", null, true, editingSectionKey === 'businessDetails')}
                                    {renderEditableDetailItem("Tax Reg. No.", getNestedValue(userData, "kyc.businessDetails.taxRegNo"), "kyc.businessDetails.taxRegNo", "text", null, true, editingSectionKey === 'businessDetails')}
                                    {renderEditableDetailItem("TIN No.", getNestedValue(userData, "kyc.businessDetails.tinNumber"), "kyc.businessDetails.tinNumber", "text", null, false, editingSectionKey === 'businessDetails')} {/* Assuming you have tinNumber */}
                                    {renderEditableDetailItem("No. of Branches", getNestedValue(userData, "kyc.businessDetails.branchCount"), "kyc.businessDetails.branchCount", "number", null, false, editingSectionKey === 'businessDetails')}
                                    {renderEditableDetailItem("Firm Nationality", getNestedValue(userData, "kyc.businessDetails.firmNationality"), "kyc.businessDetails.firmNationality", "text", null, false, editingSectionKey === 'businessDetails')}
                                    {renderEditableDetailItem("Establishment ID", getNestedValue(userData, "kyc.businessDetails.establishmentId"), "kyc.businessDetails.establishmentId", "text", null, false, editingSectionKey === 'businessDetails')}
                                    {renderEditableDetailItem("Establishment ID Issue Date", getNestedValue(userData, "kyc.businessDetails.establishmentIdIssueDate"), "kyc.businessDetails.establishmentIdIssueDate", "date", null, false, editingSectionKey === 'businessDetails')}
                                    {renderEditableDetailItem("Establishment ID Expiry Date", getNestedValue(userData, "kyc.businessDetails.establishmentIdExpiryDate"), "kyc.businessDetails.establishmentIdExpiryDate", "date", null, false, editingSectionKey === 'businessDetails')}
                                    {renderEditableDetailItem("TL Issue Date", getNestedValue(userData, "kyc.businessDetails.tlIssueDate"), "kyc.businessDetails.tlIssueDate", "date", null, false, editingSectionKey === 'businessDetails')}
                                    {renderEditableDetailItem("TL Expiry Date", getNestedValue(userData, "kyc.businessDetails.tlExpiryDate"), "kyc.businessDetails.tlExpiryDate", "date", null, false, editingSectionKey === 'businessDetails')}
                                    {renderEditableDetailItem("Ownership Type", getNestedValue(userData, "kyc.businessDetails.ownershipType"), "kyc.businessDetails.ownershipType", "text", null, false, editingSectionKey === 'businessDetails')}
                                    {renderEditableAddress(getNestedValue(userData, "kyc.businessDetails"), "kyc.businessDetails", "Business Address", false, true, editingSectionKey === 'businessDetails')}
                                    {renderEditableDetailItem("Trade License (TL) Number", getNestedValue(userData, "licenseNumber"), "licenseNumber", "text", null, false, editingSectionKey === 'businessDetails')}
                                    {renderEditableDetailItem("Business Contact Number", getNestedValue(userData, "mobileNo", getNestedValue(userData, "mobileNo")), "mobileNo", "text", null, false, editingSectionKey === 'businessDetails')}
                                    {renderEditableDetailItem("Business Email ID", getNestedValue(userData, "email", getNestedValue(userData, "email")), "email", "email", null, true, editingSectionKey === 'businessDetails')}
                                    {renderEditableDetailItem("Qatar Based", getNestedValue(userData, "kyc.businessDetails.isQatarBased"), "kyc.businessDetails.isQatarBased", "checkbox", null, false, editingSectionKey === 'businessDetails')}
                                </dl>
                            </div>
                        </div>
                    </div>

                    {/* 2. Personal Details Section */}
                    <div className="bg-white shadow rounded-lg">
                        <div
                            className="px-4 py-4 sm:px-6 border-b border-gray-200 flex justify-between items-center cursor-pointer"
                            onClick={() => toggleSectionExpand('personalDetails')}
                        >
                            <h3 className="text-lg leading-6 font-medium text-gray-900">Personal Details (Primary Applicant)</h3>
                            <div className="flex space-x-2 items-center">
                                {editingSectionKey === 'personalDetails' ? (
                                    <div className="flex space-x-2">
                                        <button type="button" onClick={(e) => { e.stopPropagation(); handleSaveSection('personalDetails'); }} disabled={isSaving && currentSavingSection === 'personalDetails'} className="px-3 py-1.5 text-xs font-medium text-white bg-green-600 rounded-md hover:bg-green-700 disabled:bg-gray-400 flex items-center">
                                            {(isSaving && currentSavingSection === 'personalDetails') ? <ArrowPathIcon className="h-4 w-4 mr-1 animate-spin" /> : <CheckIcon className="h-4 w-4 mr-1" />} Save
                                        </button>
                                        <button type="button" onClick={(e) => { e.stopPropagation(); handleCancelSectionEdit('personalDetails'); }} className="px-3 py-1.5 text-xs font-medium text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300 flex items-center">
                                            <XMarkIcon className="h-4 w-4 mr-1" /> Cancel
                                        </button>
                                    </div>
                                ) : (
                                    <button type="button" onClick={(e) => { e.stopPropagation(); handleEditSectionToggle('personalDetails'); }} className="px-3 bg-gray-200 py-1.5 text-xs font-medium text-black-600 hover:text-black-800 rounded-md border border-black-600 hover:bg-black-50 flex items-center">
                                        <PencilSquareIcon className="h-4 w-4 mr-1" /> Edit Information
                                    </button>
                                )}
                                {expandedSections.personalDetails ? <ChevronUpIcon className="h-5 w-5 text-gray-500" /> : <ChevronDownIcon className="h-5 w-5 text-gray-500" />}
                            </div>
                        </div>
                        <div
                            className={`transition-all duration-300 ease-in-out overflow-hidden ${expandedSections.personalDetails ? 'max-h-screen opacity-100' : 'max-h-0 opacity-0'}`}
                        >
                            <div className="px-4 py-5 sm:p-6">
                                <dl className="divide-y divide-gray-200">
                                    {renderEditableDetailItem("First Name", getNestedValue(userData, "firstName"), "firstName", "text", null, true, editingSectionKey === 'personalDetails')}
                                    {renderEditableDetailItem("Middle Name", getNestedValue(userData, "middleName"), "middleName", "text", null, false, editingSectionKey === 'personalDetails')}
                                    {renderEditableDetailItem("Last Name", getNestedValue(userData, "lastName"), "lastName", "text", null, true, editingSectionKey === 'personalDetails')}
                                    {renderEditableDetailItem("QID Number", getNestedValue(userData, "kyc.qidNumber"), "kyc.qidNumber", "text", null, false, editingSectionKey === 'personalDetails')}
                                    {renderEditableDetailItem("QID Expiry Date", getNestedValue(userData, "kyc.qidExpiryDate"), "kyc.qidExpiryDate", "date", null, false, editingSectionKey === 'personalDetails')}
                                    {renderEditableDetailItem("Nationality", getNestedValue(userData, "kyc.nationality"), "kyc.nationality", "text", null, false, editingSectionKey === 'personalDetails')}
                                </dl>
                            </div>
                        </div>
                    </div>

                    {/* 3. Bank Account Details Section */}
                    <div className="bg-white shadow rounded-lg">
                        <div
                            className="px-4 py-4 sm:px-6 border-b border-gray-200 flex justify-between items-center cursor-pointer"
                            onClick={() => toggleSectionExpand('bankDetails')}
                        >
                            <h3 className="text-lg leading-6 font-medium text-gray-900">Bank Account Details</h3>
                            <div className="flex space-x-2 items-center">
                                {editingSectionKey === 'bankDetails' ? (
                                    <div className="flex space-x-2">
                                        <button type="button" onClick={(e) => { e.stopPropagation(); handleSaveSection('bankDetails'); }} disabled={isSaving && currentSavingSection === 'bankDetails'} className="px-3 py-1.5 text-xs font-medium text-white bg-green-600 rounded-md hover:bg-green-700 disabled:bg-gray-400 flex items-center">
                                            {(isSaving && currentSavingSection === 'bankDetails') ? <ArrowPathIcon className="h-4 w-4 mr-1 animate-spin" /> : <CheckIcon className="h-4 w-4 mr-1" />} Save
                                        </button>
                                        <button type="button" onClick={(e) => { e.stopPropagation(); handleCancelSectionEdit('bankDetails'); }} className="px-3 py-1.5 text-xs font-medium text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300 flex items-center">
                                            <XMarkIcon className="h-4 w-4 mr-1" /> Cancel
                                        </button>
                                    </div>
                                ) : (
                                    <button type="button" onClick={(e) => { e.stopPropagation(); handleEditSectionToggle('bankDetails'); }} className="px-3 py-1.5 bg-gray-200 text-xs font-medium text-black-600 hover:text-black-800 rounded-md border border-black-600 hover:bg-black-50 flex items-center">
                                        <PencilSquareIcon className="h-4 w-4 mr-1" /> Edit Information
                                    </button>
                                )}
                                {expandedSections.bankDetails ? <ChevronUpIcon className="h-5 w-5 text-gray-500" /> : <ChevronDownIcon className="h-5 w-5 text-gray-500" />}
                            </div>
                        </div>
                        <div
                            className={`transition-all duration-300 ease-in-out overflow-hidden ${expandedSections.bankDetails ? 'max-h-screen opacity-100' : 'max-h-0 opacity-0'}`}
                        >
                            <div className="px-4 py-5 sm:p-6">
                                <dl className="divide-y divide-gray-200">
                                    {renderEditableDetailItem("Account Number", getNestedValue(userData, "kyc.incomeDetails.accountNumber"), "kyc.incomeDetails.accountNumber", "text", null, true, editingSectionKey === 'bankDetails')}
                                    {renderEditableDetailItem("IBAN", getNestedValue(userData, "kyc.incomeDetails.ifscCode"), "kyc.incomeDetails.ifscCode", "text", null, true, editingSectionKey === 'bankDetails')}
                                </dl>
                            </div>
                        </div>
                    </div>

                    {/* 4. Uploaded Documents Section */}
                    <div className="bg-[#dfe5e5] shadow rounded-lg">
                        <div
                            className="px-4 py-4 sm:px-6 border-b border-gray-200 bg-white rounded-t-lg flex justify-between items-center cursor-pointer"
                            onClick={() => toggleSectionExpand('uploadedDocuments')}
                        >
                            <h3 className="text-lg leading-6 font-medium text-gray-900">Uploaded Documents</h3>
                            <div className="flex space-x-2 items-center">
                                {editingSectionKey === 'uploadedDocuments' ? (
                                    <div className="flex space-x-2">
                                        <button type="button" onClick={(e) => { e.stopPropagation(); handleEditSectionToggle('uploadedDocuments'); }} className="px-3 py-1.5 text-xs font-medium text-gray-700 bg-green-600 hover:bg-green-700 text-white rounded-md flex items-center">
                                            <CheckIcon className="h-4 w-4 mr-1" /> Done Editing Documents
                                        </button>
                                    </div>
                                ) : (
                                    <button type="button" onClick={(e) => { e.stopPropagation(); handleEditSectionToggle('uploadedDocuments'); }} className="px-3 bg-gray-200 py-1.5 text-xs font-medium text-black-600 hover:text-black-800 rounded-md border border-black-600 hover:bg-black-50 flex items-center">
                                        <PencilSquareIcon className="h-4 w-4 mr-1" /> Edit Documents
                                    </button>
                                )}
                                {expandedSections.uploadedDocuments ? <ChevronUpIcon className="h-5 w-5 text-gray-500" /> : <ChevronDownIcon className="h-5 w-5 text-gray-500" />}
                            </div>
                        </div>
                        <div
                            className={`transition-all duration-300 ease-in-out overflow-hidden ${expandedSections.uploadedDocuments ? 'max-h-screen opacity-100' : 'max-h-0 opacity-0'}`}
                        >
                            <div className="px-4 py-5 sm:p-6">
                                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                                    {renderDocument(getNestedValue(formData, "commercialRegistration"), "Commercial Registration", "commercialRegistration", null, false, editingSectionKey === 'uploadedDocuments')}
                                    {renderDocument(getNestedValue(formData, "tradeLicense"), "Trade License", "tradeLicense", null, false, editingSectionKey === 'uploadedDocuments')}
                                    {renderDocument(getNestedValue(formData, "taxCard"), "Tax Card", "taxCard", null, false, editingSectionKey === 'uploadedDocuments')}
                                    {renderDocument(getNestedValue(formData, "establishmentCard"), "Establishment Card", "establishmentCard", null, false, editingSectionKey === 'uploadedDocuments')}
                                    {renderDocument(getNestedValue(formData, "bankStatement"), "Business Bank Statement (Last 6 Months)", "bankStatement", null, false, editingSectionKey === 'uploadedDocuments')}
                                    {renderDocument(getNestedValue(formData, "auditedFinancialReport"), "Audited Financial Report (Latest)", "auditedFinancialReport", null, false, editingSectionKey === 'uploadedDocuments')}
                                    {renderDocument(getNestedValue(formData, "cashFlowLedger"), "Cash Flow/Ledger", "cashFlowLedger", null, false, editingSectionKey === 'uploadedDocuments')}
                                    {renderDocument(getNestedValue(formData, "commercialCreditReport"), "Commercial Credit Report (CCR)", "commercialCreditReport", null, false, editingSectionKey === 'uploadedDocuments')}
                                    {renderDocument(getNestedValue(formData, "memorandumOfAssociation"), "Memorandum of Association (MOA)", "memorandumOfAssociation", null, false, editingSectionKey === 'uploadedDocuments')}
                                    {renderDocument(getNestedValue(formData, "articleOfAssociation"), "Article of Association (AOA)", "articleOfAssociation", null, false, editingSectionKey === 'uploadedDocuments')}
                                    {renderDocument(getNestedValue(formData, "kyc.qatariId"), "Qatari ID (Primary Applicant)", "qatariId", null, false, editingSectionKey === 'uploadedDocuments')}
                                    {renderDocument(getNestedValue(formData, "kyc.passport"), "Passport (Primary Applicant)", "passport", null, false, editingSectionKey === 'uploadedDocuments')}
                                    {renderDocument(getNestedValue(formData, "kyc.utilityBill"), "Utility Bill (Proof of Address)", "utilityBill", null, false, editingSectionKey === 'uploadedDocuments')}
                                    {/* Add otherDocument3 to otherDocument10 if they exist and are used */}
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* 5. Shareholders (No section-level edit button for this page) */}
                    {shareholders.length > 0 && (
                        <div className="bg-[#dfe5e5] shadow rounded-lg">
                            <div
                                className="px-4 py-4 sm:px-6 border-b bg-white rounded-t-lg border-gray-200 flex justify-between items-center cursor-pointer"
                                onClick={() => toggleSectionExpand('shareholders')}
                            >
                                <h3 className="text-lg leading-6 font-medium text-gray-900">Shareholder Details</h3>
                                {expandedSections.shareholders ? <ChevronUpIcon className="h-5 w-5 text-gray-500" /> : <ChevronDownIcon className="h-5 w-5 text-gray-500" />}
                            </div>
                            <div
                                className={`transition-all duration-300 ease-in-out overflow-hidden ${expandedSections.shareholders ? 'max-h-[2000px] opacity-100' : 'max-h-0 opacity-0'}`}
                            >
                                <div className="px-4 py-5 sm:p-6 space-y-4">
                                    {shareholders.map((_, index) => {
                                        const originalSh = userData?.shareholders?.[index] || {};
                                        const shareholderSectionKey = `shareholder-${index}`;
                                        // For this page, shareholder item editing is disabled by passing false for isEditingThisSection
                                        const isEditingThisShareholder = false; // Or based on a more granular editing state if needed
                                        return (
                                            <div key={originalSh._id || index} className={`p-4 border rounded-md bg-gray-50`}>
                                                <div className="flex justify-between items-center mb-2">
                                                    <p className="font-semibold text-gray-700">Shareholder {index + 1}</p>
                                                    {/* No Edit button for individual shareholder on this main review page */}
                                                </div>
                                                <dl className="divide-y divide-gray-100">
                                                    {renderEditableDetailItem("First Name", originalSh.firstName, `shareholders.${index}.firstName`, "text", null, false, isEditingThisShareholder)}
                                                    {renderEditableDetailItem("Last Name", originalSh.lastName, `shareholders.${index}.lastName`, "text", null, false, isEditingThisShareholder)}
                                                    {renderEditableDetailItem("Email", originalSh.email, `shareholders.${index}.email`, "email", null, false, isEditingThisShareholder)}
                                                    {renderEditableAddress(originalSh.address, `shareholders.${index}.address`, "Shareholder Address", false, false, isEditingThisShareholder)}
                                                    <div className="pt-3">
                                                        <h4 className="text-sm font-medium text-gray-600 mb-1">Documents</h4>
                                                        <div className="space-y-2">
                                                            {renderDocument(getNestedValue(formData, `shareholders.${index}.passport`), "Passport", 'shareholderPassport', index, false, editingSectionKey === 'uploadedDocuments')}
                                                            {renderDocument(getNestedValue(formData, `shareholders.${index}.qid`), "QID", 'shareholderQid', index, false, editingSectionKey === 'uploadedDocuments')}
                                                        </div>
                                                    </div>
                                                </dl>
                                            </div>
                                        );
                                    })}
                                </div>
                            </div>
                        </div>
                    )}

                    {/* 6. Buyers (No section-level edit button for this page) */}
                    {buyers.length > 0 && (
                        <div className="bg-[#dfe5e5] shadow rounded-lg">
                            <div
                                className="px-4 py-4 bg-white rounded-t-lg sm:px-6 border-b border-gray-200 flex justify-between items-center cursor-pointer"
                                onClick={() => toggleSectionExpand('buyers')}
                            >
                                <h3 className="text-lg leading-6 font-medium text-gray-900">Buyer Details</h3>
                                {expandedSections.buyers ? <ChevronUpIcon className="h-5 w-5 text-gray-500" /> : <ChevronDownIcon className="h-5 w-5 text-gray-500" />}
                            </div>
                            <div
                                className={`transition-all duration-300 ease-in-out overflow-hidden ${expandedSections.buyers ? 'max-h-[2000px] opacity-100' : 'max-h-0 opacity-0'}`}
                            >
                                <div className="px-4 py-5 sm:p-6 space-y-4">
                                    {buyers.map((_, index) => {
                                        const originalBuyer = userData?.kyc?.buyers?.[index] || {};
                                        const buyerSectionKey = `buyer-${index}`;
                                        const isEditingThisBuyer = false; // Buyer editing disabled on this main review page
                                        return (
                                            <div key={originalBuyer._id || index} className={`p-4 border rounded-md bg-gray-50`}>
                                                <p className="font-semibold text-gray-700 mb-2">Buyer {index + 1}</p>
                                                <dl className="divide-y divide-gray-100">
                                                    {renderEditableBuyerItem("Name", originalBuyer.buyerName, 'kyc.buyers', index, 'buyerName', 'text', true, isEditingThisBuyer)}
                                                    {renderEditableBuyerItem("Contact Email", originalBuyer.contactEmail, 'kyc.buyers', index, 'contactEmail', 'email', true, isEditingThisBuyer)}
                                                    {renderEditableBuyerItem("CRN / Registration", originalBuyer.registrationNumber, 'kyc.buyers', index, 'registrationNumber', 'text', false, isEditingThisBuyer)}
                                                    {renderEditableBuyerItem("Contact Person", originalBuyer.contactPerson, 'kyc.buyers', index, 'contactPerson', 'text', false, isEditingThisBuyer)}
                                                    {renderEditableBuyerItem("Contact Phone", originalBuyer.contactPhone, 'kyc.buyers', index, 'contactPhone', 'text', false, isEditingThisBuyer)}
                                                </dl>
                                            </div>
                                        );
                                    })}
                                </div>
                            </div>
                        </div>
                    )}

                    {/* 7. Directors (Display Only) */}
                    {(directors.length > 0) && (
                        <div className="bg-white shadow rounded-lg">
                            <div className="px-4 py-4 sm:px-6 border-b border-gray-200">
                                <h3 className="text-lg leading-6 font-medium text-gray-900">Directors</h3>
                            </div>
                            <div className="px-4 py-5 sm:p-6">
                                <div className="bg-gray-50 p-3 rounded-md border divide-y divide-gray-200">
                                    {directors.map((dir, index) => (
                                        <div key={dir._id || index} className="py-2">
                                            <p className="text-sm font-medium text-gray-800">{dir.directorName || 'N/A'}</p>
                                            <p className="text-xs text-gray-600">
                                                {dir.position || 'Position N/A'}
                                                {dir.isUBO && " | UBO"}
                                                {(dir.contactEmail || dir.contactPhone) && ` | ${dir.contactEmail || dir.contactPhone}`}
                                            </p>
                                        </div>
                                    ))}
                                </div>
                            </div>
                        </div>
                    )}

                    {/* Consent and FINAL Submission */}
                    {!editingSectionKey && ( // Only show final submission if no section is being actively edited
                        <div className="mt-10 pt-6 border-t border-gray-200">
                            <h3 className="text-md font-semibold text-gray-700 mb-3 flex items-center">
                                <InformationCircleIcon className="h-5 w-5 mr-2 text-gray-500" /> Declarations and Consent
                            </h3>
                            <div className="space-y-3 mb-6 bg-gray-50 p-4 rounded-md border border-gray-200">
                                <div className="flex items-start">
                                    <input id="agreeAccurate" name="agreeAccurate" type="checkbox" checked={agreedAccurate} onChange={(e) => setAgreedAccurate(e.target.checked)} className="h-4 w-4 text-black-600 border-gray-300 rounded mt-0.5 focus:ring-black-500" />
                                    <label htmlFor="agreeAccurate" className="ml-3 block text-sm text-gray-700">I confirm the accuracy and completeness of all information provided.</label>
                                </div>
                                <div className="flex items-start">
                                    <input id="readTerms" name="readTerms" type="checkbox" checked={readTerms} onChange={(e) => setReadTerms(e.target.checked)} className="h-4 w-4 text-black-600 border-gray-300 rounded mt-0.5 focus:ring-black-500" />
                                    <label htmlFor="readTerms" className="ml-3 block text-sm text-gray-700">I agree to the <a href="/terms-and-conditions" target="_blank" rel="noopener noreferrer" className="text-black-600 underline hover:text-black-800">Terms & Conditions</a> and <a href="/privacy-policy" target="_blank" rel="noopener noreferrer" className="text-black-600 underline hover:text-black-800">Privacy Policy</a>.</label>
                                </div>
                                <div className="flex items-start">
                                    <input id="readFinancialConsent" name="readFinancialConsent" type="checkbox" checked={readFinancialConsent} onChange={(e) => setReadFinancialConsent(e.target.checked)} className="h-4 w-4 text-black-600 border-gray-300 rounded mt-0.5 focus:ring-black-500" />
                                    <label htmlFor="readFinancialConsent" className="ml-3 block text-sm text-gray-700">I agree to the <button type="button" onClick={openConsentModal} className="text-black-600 underline hover:text-black-800">Financial Data & Credit Bureau Report Consent</button>.</label>
                                </div>
                            </div>

                            {submitError && <p className="text-red-600 text-center mb-4 text-sm p-2 bg-red-50 border border-red-300 rounded">{submitError}</p>}

                            <div className="flex gap-4 items-center justify-center mt-8">
                                {onBack && (
                                    <button type="button" onClick={onBack} className="px-6 py-2.5 bg-[#60777c] rounded-md hover:bg-gray-700 text-white font-medium focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 text-sm">
                                        Back
                                    </button>
                                )}
                                <button
                                    type="submit"
                                    disabled={!agreedAccurate || !readTerms || !readFinancialConsent || isSubmitting || missingFieldLabels.length > 0 || (userData?.kyc?.verificationStatus === "UNDER_REVIEW")}
                                    className={`inline-flex justify-center items-center px-6 py-2.5 ${(!agreedAccurate || !readTerms || !readFinancialConsent || isSubmitting || missingFieldLabels.length > 0) ? 'bg-gray-400 cursor-not-allowed' : 'bg-[#023a3b] hover:bg-green-950'} text-white font-semibold rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition duration-150 ease-in-out text-sm disabled:opacity-60`}
                                >
                                    {isSubmitting ? (<> <ArrowPathIcon className="w-4 h-4 mr-2 animate-spin" /> Submitting...</>)
                                        : (missingFieldLabels.length > 0 ? 'Complete Required Fields to Submit' : 'Agree & Submit Application')}
                                </button>
                            </div>
                        </div>
                    )}
                </form> {/* End Form */}

                {/* Modals (Keep existing) */}
                <Modal
                    isOpen={isDocViewerOpen}
                    onRequestClose={closeDocumentViewer}
                    contentLabel="Document Viewer"
                    style={{
                        overlay: { backgroundColor: 'rgba(0, 0, 0, 0.75)', zIndex: 1050 },
                        content: {
                            top: '5%', left: '5%', right: '5%', bottom: '5%',
                            padding: '0', border: 'none', borderRadius: '8px',
                            display: 'flex', flexDirection: 'column'
                        }
                    }}
                    ariaHideApp={false}
                >
                    <div className="flex justify-between items-center p-3 bg-gray-100 border-b rounded-t-lg flex-shrink-0">
                        <h2 className="text-lg font-semibold">Document Preview</h2>
                        <button onClick={closeDocumentViewer} className="text-gray-500 hover:text-gray-800"><XMarkIcon className="h-6 w-6" /></button>
                    </div>
                    <div className="flex-grow overflow-hidden">
                        {docViewerUrl ? (
                            <iframe src={docViewerUrl} width="100%" height="100%" title="Document View" style={{ border: 'none' }} />
                        ) : (
                            <p className="p-4 text-center text-gray-500">Could not load document URL.</p>
                        )}
                    </div>
                </Modal>
                <Modal
                    isOpen={isConsentModalOpen}
                    onRequestClose={closeConsentModal}
                    contentLabel="Financial Data Consent"
                    style={{
                        overlay: { backgroundColor: 'rgba(0, 0, 0, 0.75)', zIndex: 1050 },
                        content: {
                            top: '50%', left: '50%', transform: 'translate(-50%, -50%)',
                            width: '90%', maxWidth: '700px', maxHeight: '80vh',
                            padding: '0', border: 'none', borderRadius: '8px',
                            overflow: 'hidden', display: 'flex', flexDirection: 'column'
                        }
                    }}
                    ariaHideApp={false}
                >
                    <div className="flex justify-between items-center p-4 bg-gray-100 border-b rounded-t-lg flex-shrink-0">
                        <h2 className="text-lg font-semibold">Financial Data & Credit Bureau Report Consent</h2>
                        <button onClick={closeConsentModal} className="text-gray-500 hover:text-gray-800"><XMarkIcon className="h-6 w-6" /></button>
                    </div>
                    <div className="p-6 prose max-w-none overflow-y-auto">
                        {/* Your existing consent text goes here */}
                        <h1 className="text-2xl font-bold text-gray-800 mb-4">Consent Form – Credit Report</h1>
                        <div className="mb-6">
                            <h2 className="text-xl font-semibold text-gray-700 mb-2">First: By signing this consent I hereby agree to the following:</h2>
                            <ol className="list-decimal pl-6 space-y-2 text-sm">
                                <li>Madad Financial Technologies, its partners and lenders will inquire about my credit history including returned checks...</li>
                                <li>Madad Financial Technologies, its partners and lenders will review all facilities granted to me...</li>
                                <li>I acknowledge that Madad Financial Technologies, its partners and lenders are required to provide my credit information to Qatar Credit Information Center...</li>
                                <li>Madad Financial Technologies, its partners and lenders may request a credit history report more than once...</li>
                                <li>Madad Financial Technologies, its partners and lenders has the right to request my credit report at any time...</li>
                                <li>Madad Financial Technologies, its partners and lenders and its employees are exempted from any liability...</li>
                                <li>I undertake to inform Madad Financial Technologies, its partners and lenders of any significant financial obligation...</li>
                            </ol>
                        </div>
                        <div className="mb-6">
                            <h2 className="text-xl font-semibold text-gray-700 mb-2">Second: I also acknowledge and agree to the following:</h2>
                            <ol className="list-decimal pl-6 text-sm">
                                <li>I understand that all of the above applies to me, whether an individual, institution or company...</li>
                            </ol>
                        </div>
                        <p className="text-sm"> I confirm that I have read and understood the above and agree to the terms and conditions outlined herein. </p>
                    </div>
                    <div className="p-4 bg-gray-50 border-t flex justify-end rounded-b-lg flex-shrink-0">
                        <button onClick={closeConsentModal} className="px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 text-sm font-medium">Close</button>
                    </div>
                </Modal>

            </div> {/* End Max Width Container */}
            {(isLoading || isSaving || isSubmitting) && (
                <LoadingPopup />
            )}
        </div> /* End Main Component Div */
    );
};

export default ReviewAndSubmit;


const parseDMYtoYMD = (dmyString) => {
    if (!dmyString || typeof dmyString !== 'string') return null;
    const parts = dmyString.split('/');
    if (parts.length === 3) {
        const day = parts[0].padStart(2, '0');
        const month = parts[1].padStart(2, '0');
        const year = parts[2];
        if (day.length === 2 && month.length === 2 && year.length === 4) {
            return `${year}-${month}-${day}`;
        }
    }
    return null; // Return null if parsing fails, or you could return the original dmyString
};