import React, { useState, useEffect, useCallback } from 'react';
import { useHistory } from 'react-router-dom';
import SharedCache from '../../../sharedCache';
// Assuming these API functions exist in this path
import { getKycInfo, initiateVideoKyc, getVideoKycVerificationStatus } from '../../../api/kyc';
import config from '../../../config.json';
import {
    ClockIcon,
    ArrowPathIcon,
    ExclamationTriangleIcon,
    CheckCircleIcon,
    XCircleIcon,
    ArrowRightCircleIcon,
    QuestionMarkCircleIcon,
    VideoCameraIcon, // Icon for the Video KYC button
    XMarkIcon // Icon for modal close button
} from '@heroicons/react/24/outline';
import LoadingModal from '../../Reusable/Loading';


// Helper function (keep as is)
const getNested = (obj, path, defaultValue = null) => {
    try {
        const value = path.split('.').reduce((o, k) => (o || {})[k], obj);
        return value === undefined || value === null ? defaultValue : value;
    } catch (e) {
        return defaultValue;
    }
};

const LoadingPopup = ({ fileName }) => {
    return <LoadingModal message='Please wait a moment while we load your journey data.' />
};


const ShareholderKycStatus = ({ onBack, onNext }) => {
    const [userData, setUserData] = useState(null);
    const [isLoading, setIsLoading] = useState(true); // Loading initial page data
    const [error, setError] = useState('');
    const history = useHistory();
    const [kycStatus, setKycStatus] = useState(null); // Overall KYC status
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [isVideoKycAttempted, setIsVideoKycAttempted] = useState(false); // Tracks if modal *was* opened
    const [isInitiatingKyc, setIsInitiatingKyc] = useState(false); // Loading state for the init API call
    const [iframeSrc, setIframeSrc] = useState(''); // State to hold the dynamic iframe URL from API
    const [isCheckingStatus, setIsCheckingStatus] = useState(false); // Loading state for status check after close

    // Removed static kycJourneyLink

    // sendBulkEmails - keep as is, but maybe update the link later if needed
    const sendBulkEmails = async (emails) => {
        try {
            const response = await fetch(`${config.apiUrl}/ops/invoiceFinancing/send-bulk-emails`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json', },
                body: JSON.stringify({
                    emails: emails || [],
                    subject: 'KYC Verification',
                    // Consider if this email link should also be dynamic or removed if KYC is only via app
                    html: `Your KYC has been initiated, <a href="https://app.shuftipro.com/verification/journey/SxkSHnDo1745353789" target="_blank">click here to start the KYC process</a>.`
                }),
            });
            if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);
            const data = await response.json();
            console.log("Bulk emails sent:", data);
        } catch (error) {
            console.error("Error sending bulk emails:", error);
        }
    };

    // fetchData - Initial data load (keep as is)
    const fetchData = useCallback(async (userId) => {
        setIsLoading(true);
        setError('');
        try {
            console.log(`Workspaceing KYC info for status page: userId=${userId}`);
            const response = await getKycInfo(userId); // Assumes this fetches the full user object
            console.log("getKycInfo Response:", response);
            if (response?.success && response?.user) {
                const data = response.user;
                const mainKycStatus = getNested(data, 'kyc.verificationStatus', null);
                setKycStatus(mainKycStatus);
                data.shareholders = data.shareholders || [];
                setUserData(data); // Store the full user data
                console.log("User data set for status page:", data);

                // Update isVideoKycAttempted based on fetched status if needed
                const currentVideoStatus = getNested(data, 'kyc.videoKyc.status', 'NOT_ATTEMPTED');
                if (currentVideoStatus !== 'NOT_ATTEMPTED') {
                    setIsVideoKycAttempted(true);
                }

                // Email logic (keep or adjust as needed)
                // if (mainKycStatus === 'INITIATED' || !mainKycStatus) {
                //    await sendBulkEmails(data?.shareholders?.map(sh => sh.email).filter(Boolean));
                // }
            } else {
                setError(response?.message || "Could not retrieve user data.");
                setKycStatus(null);
            }
        } catch (err) {
            console.error("Error fetching data for status page:", err);
            setError(`Error loading data: ${err.message}.`);
            setKycStatus(null);
        } finally {
            setIsLoading(false);
        }
    }, []);

    // useEffect for initial data load and email sending
    // useEffect for initial data load and sending emails once on mount
    useEffect(() => {
        const user = SharedCache.get('user') || {};
        const userId = user._id || user.id || "";

        if (!userId) {
            setError("User ID not found. Please log in again.");
            setIsLoading(false);
            return;
        }

        const fetchDataAndSendEmails = async () => {
            // Fetch user data first
            await fetchData(userId);

            // Access the *latest* userData state directly here.
            // Although userData is not a dependency, this code runs
            // within the same async flow triggered by the effect,
            // shortly after fetchData has likely completed and updated state.
            // For this pattern (initial fetch then action), this is generally safe.

            // Access the current state value - this can be slightly tricky
            // if state updates are very rapid, but for initial load is standard.
            // A more robust way for complex scenarios might involve a ref
            // to track if emails were sent. For this case, relying on
            // fetchData completing before the next lines execute is typical.

            // A slight delay or checking the state value after a tick might be
            // needed in more complex scenarios, but let's try direct access first.
            // console.log("Checking userData after fetchData:", userData); // Debugging line

            // Using a simple check based on the assumption that setUserData
            // from fetchData will complete before the email sending code runs
            // within the same async function.
            // Alternatively, you could pass the fetched user data directly
            // from fetchData if it returned the data instead of just updating state.

            // Re-fetch user data directly inside this async function to ensure latest state
            // try {
            //     const response = await getKycInfo(userId); // Fetch again just for this logic
            //     if (response?.success && response?.user) {
            //         const latestUserData = response.user;
            //         const shareholderEmails = latestUserData.shareholders?.map(sh => sh.email).filter(Boolean) || [];
            //         const allEmails = [...shareholderEmails, latestUserData?.email].filter(Boolean); // Include user's primary email

            //         // Add a condition to send emails only if it hasn't been sent before
            //         // You would need a state or ref to track this. Let's add a simple state flag.
            //         // For this example, let's assume sending once per page load is the goal.
            //         // If you need to prevent sending on subsequent renders due to other state changes,
            //         // you'll need a ref.

            //         // Let's use a simple flag to prevent sending on subsequent renders IF
            //         // the effect re-ran due to other dependencies (fetchData or sendBulkEmails, though stable).
            //         // For a true "only once on mount" behavior, an empty dependency array is best,
            //         // but that wouldn't allow using fetchData or sendBulkEmails easily without refs
            //         // or restructuring.

            //         // A better approach for "send once after initial fetch":
            //         // Use a ref to track if emails have been sent.

            //         // We need to refactor slightly to use a ref reliably.
            //         // Let's stick to the simpler approach first and address
            //         // the ref if needed, but the primary issue is userData dependency.

            //         // Let's remove userData from deps and trust the async flow
            //         // along with potentially re-fetching the data here for the email logic.

            //         if (allEmails.length > 0) {
            //             console.log("Attempting to send bulk emails to:", allEmails);
            //             await sendBulkEmails(allEmails);
            //         } else {
            //             console.log("No valid email addresses found for bulk sending.");
            //         }
            //     }
            // } catch (err) {
            //     console.error("Error re-fetching data for email sending:", err);
            //     // Handle error if needed
            // }
        };

        fetchDataAndSendEmails();

    }, []); // Removed userData from dependencies

    // useEffect for initial data load (keep as is)
    useEffect(() => {
        const user = SharedCache.get('user') || {};
        const userId = user._id || user.id || "";
        if (!userId) {
            setError("User ID not found. Please log in again.");
            setIsLoading(false);
            return;
        }
        fetchData(userId);
    }, [fetchData]);


    // UPDATED Function to handle opening the modal
    const handleOpenModal = async () => {
        setIsInitiatingKyc(true);
        setError('');
        const user = SharedCache.get('user') || {};
        const userId = user._id || user.id || "";
        if (!userId) {
            setError("User ID not found. Cannot initiate KYC.");
            setIsInitiatingKyc(false);
            return;
        }
        try {
            const result = await initiateVideoKyc(userId, null); // Call backend to initiate
            if (result.success && result.verificationUrl) {
                console.log("Video KYC initiated successfully. Ref:", result.reference);
                setIframeSrc(result.verificationUrl); // Set dynamic URL
                setIsModalOpen(true); // Open modal
            } else {
                console.error("Failed to initiate Video KYC:", result.error);
                const errorMessage = `Failed to start Video KYC: ${result.error || 'Unknown error from server.'}`;
                setError(errorMessage);
                alert(errorMessage);
                setIframeSrc('');
                setIsModalOpen(false);
            }
        } catch (apiError) {
            console.error("Error calling initiation API:", apiError);
            const errorMessage = `Error initiating Video KYC: ${apiError.message || 'Please check network connection.'}`;
            setError(errorMessage);
            alert(errorMessage);
            setIframeSrc('');
            setIsModalOpen(false);
        } finally {
            setIsInitiatingKyc(false);
        }
    };

    // UPDATED Function to handle closing the modal
    const handleCloseModal = async () => {
        setIsModalOpen(false);
        setIsVideoKycAttempted(true);
        setIframeSrc('');
        setIsCheckingStatus(true); // Start status check loading
        setError('');
        console.log("Video KYC modal closed. Fetching latest status immediately...");
        try {
            const user = SharedCache.get('user') || {};
            const userId = user._id || user.id || "";
            const statusResult = await getVideoKycVerificationStatus(userId); // Call status check API
            if (statusResult.success) {
                console.log("Fetched latest status:", statusResult);
                // Update component state with the fetched status by merging into userData
                setUserData(prevData => {
                    const prevKyc = prevData?.kyc || {};
                    const prevVideoKyc = prevKyc.videoKyc || {};
                    return {
                        ...prevData,
                        kyc: {
                            ...prevKyc,
                            verificationStatus: statusResult.overallKycStatus,
                            videoKyc: {
                                ...prevVideoKyc,
                                status: statusResult.videoKycStatus,
                                declineReason: statusResult.declineReason
                            }
                        }
                    };
                });
                setKycStatus(statusResult.overallKycStatus); // Update overall status state
            } else {
                console.error("Failed to fetch latest status:", statusResult.error);
                alert(`Could not retrieve the latest KYC status automatically. The status shown might be outdated. Error: ${statusResult.error}`);
            }
        } catch (fetchError) {
            console.error("Error calling status fetch API:", fetchError);
            alert(`An error occurred while fetching the latest KYC status. The status shown might be outdated.`);
        } finally {
            setIsCheckingStatus(false); // Stop status check loading
        }
    };

    // renderStatus - Keep as is (renders status badges)
    const renderStatus = (status) => {
        let colorClass = 'text-gray-800 bg-gray-100';
        let icon = <QuestionMarkCircleIcon className="w-4 h-4 mr-1.5 text-gray-500" aria-hidden="true" />;
        let statusText = status || 'UNKNOWN';
        switch (status) {
            case 'APPROVED': case 'VERIFIED':
                colorClass = 'text-green-800 bg-green-100'; icon = <CheckCircleIcon className="w-4 h-4 mr-1.5 text-green-500" aria-hidden="true" />; statusText = 'Approved'; break;
            case 'INITIATED':
                colorClass = 'text-gray-800 bg-gray-100'; icon = <ClockIcon className="w-4 h-4 mr-1.5 text-gray-500" aria-hidden="true" />; statusText = 'Initiated'; break;
            case 'REINITIATED':
                colorClass = 'text-blue-800 bg-blue-100'; icon = <ArrowPathIcon className="w-4 h-4 mr-1.5 text-blue-500" aria-hidden="true" />; statusText = 'Re-initiated'; break;
            case 'UNDER_REVIEW': case 'REVIEW': case 'PENDING':
                colorClass = 'text-yellow-800 bg-yellow-100'; icon = <ArrowPathIcon className="w-4 h-4 mr-1.5 text-yellow-500" aria-hidden="true" />; statusText = 'Under Review'; break;
            case 'INFO_NEEDED':
                colorClass = 'text-orange-800 bg-orange-100'; icon = <ExclamationTriangleIcon className="w-4 h-4 mr-1.5 text-orange-500" aria-hidden="true" />; statusText = 'Info Needed'; break;
            case 'REJECTED':
                colorClass = 'text-red-800 bg-red-100'; icon = <XCircleIcon className="w-4 h-4 mr-1.5 text-red-500" aria-hidden="true" />; statusText = 'Rejected'; break;
            default: statusText = status ? status.replace(/_/g, ' ') : 'Unknown'; break;
        }
        return <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${colorClass} whitespace-nowrap`}>{icon}{statusText}</span>;
    };

    // renderCompletionDate - Keep as is
    const renderCompletionDate = (dateString) => {
        if (!dateString) return '-';
        try {
            const date = new Date(dateString);
            if (isNaN(date.getTime())) return '-';
            return date.toLocaleDateString();
        } catch (e) {
            console.error("Error parsing date:", dateString, e); return '-';
        }
    };

    // handleResend - Keep as is (uses sendBulkEmails)
    const handleResend = async (email, name) => {
        if (!email || email === 'N/A') { alert('Cannot resend: Email address is missing.'); return; }
        alert(`Attempting to resend eKYC link to ${name || 'the user'} at ${email}.`);
        try { await sendBulkEmails([email]); alert(`eKYC link resent successfully to ${email}.`); }
        catch (error) { alert(`Failed to resend eKYC link to ${email}. Error: ${error.message}`); }
    };

    // handleFinish - Keep as is
    const handleFinish = () => {
        onNext(userData?.shareholders || []);
    };

    // Loading/Error States - Keep as is
    if (isLoading) { /* ... Loading JSX ... */
        return <LoadingPopup />;
    }
    // Note: Keep the top-level error display, but clear specific API errors with setError('') before new calls
    // if (error && !isInitiatingKyc && !isCheckingStatus) { /* ... Error JSX ... */
    //      return ( <div className="max-w-4xl mx-auto p-6 my-10 border border-red-400 bg-red-50 text-red-700 rounded-md text-center"><ExclamationTriangleIcon className="h-6 w-6 inline mr-2" />{error}</div> );
    // }

    // Prepare shareholder data (adjust nested paths if needed)
    const shareholdersToDisplay = (userData?.shareholders || []).map((sh, index) => ({
        id: sh._id || `sh-${index}`,
        name: `${sh.firstName || ''} ${sh.lastName || ''}`.trim() || `Shareholder ${index + 1}`,
        // Assuming shareholder status is directly on the shareholder object
        kycVerificationStatus: sh.kycVerificationStatus || null,
        email: sh.email || 'N/A',
        type: 'Shareholder',
        // Use shareholder's modifiedOn or a specific completion date if available
        completionDate: sh.modifiedOn, // Or getNested(sh, 'kyc.completionDate', sh.modifiedOn)
    }));

    // Determine if the "Next" button should be enabled
    // Now potentially based on actual *approved* status, not just attempted
    // Example: Enable Next only if overall KYC is Approved
    const overallStatusFromState = getNested(userData, 'kyc.verificationStatus');
    // Simple logic: attempt needed. More robust: check overallStatusFromState === 'APPROVED'
    const isNextButtonEnabled = isVideoKycAttempted; // Or: overallStatusFromState === 'APPROVED';


    // --- Render JSX ---
    return (
        <div className="bg-[#f0f0f0] shadow-lg rounded-lg"> {/* Container for title and content */}
            <div className="px-4 sm:px-6 bg-white py-4 border-b border-gray-200">
                <h1 className="text-xl sm:text-2xl font-bold text-gray-800"> {/* Responsive title */}
                    Shareholder(s) eKYC Status
                </h1>
                <p className="mt-1 text-xs sm:text-sm text-gray-600">
                    eKYC is triggered for the shareholders/beneficial owners listed below. Please complete to process your application.
                </p>
            </div>
            <div className="bg-[#f0f0f0] py-4 sm:py-6 px-2 sm:px-4 lg:px-8">
                <div className="max-w-full mx-auto">
                    {/* Shareholder Status Table */}
                    <div className="overflow-x-auto">
                        <div className="shadow border border-gray-200 overflow-hidden rounded-lg min-w-full">
                            {/* Mobile Card View */}
                            <div className="block sm:hidden">
                                {shareholdersToDisplay.length === 0 && !isLoading && (
                                    <div className="bg-white p-4 text-center text-sm text-gray-500 italic">
                                        No shareholders have been added.
                                    </div>
                                )}
                                {shareholdersToDisplay.map((person, index) => (
                                    <div key={person.id || index} className="bg-white border-b border-gray-200 p-4">
                                        <div className="space-y-2">
                                            <div className="flex justify-between items-start">
                                                <span className="text-xs font-medium text-gray-500 uppercase">Serial No.</span>
                                                <span className="text-sm font-medium text-gray-900">{index + 1}</span>
                                            </div>
                                            <div className="flex justify-between items-start">
                                                <span className="text-xs font-medium text-gray-500 uppercase">Name</span>
                                                <span className="text-sm font-medium text-gray-900 text-right">{person.name}</span>
                                            </div>
                                            <div className="flex justify-between items-start">
                                                <span className="text-xs font-medium text-gray-500 uppercase">Email</span>
                                                <div className="flex flex-col items-end space-y-1">
                                                    <span className="text-sm text-gray-500 text-right">{person.email}</span>
                                                    {(person.email && person.email !== 'N/A' && person.kycVerificationStatus !== 'APPROVED' && person.kycVerificationStatus !== 'VERIFIED' && person.kycVerificationStatus !== 'REJECTED') && (
                                                        <button
                                                            onClick={() => handleResend(person.email, person.name)}
                                                            className="text-[#60c193] hover:text-green-400 text-xs font-medium hover:underline disabled:text-gray-400 disabled:cursor-not-allowed"
                                                        >
                                                            Resend Link
                                                        </button>
                                                    )}
                                                </div>
                                            </div>
                                            <div className="flex justify-between items-start">
                                                <span className="text-xs font-medium text-gray-500 uppercase">KYC Status</span>
                                                <span className="text-sm text-gray-500 text-right">{renderStatus(person.kycVerificationStatus)}</span>
                                            </div>
                                            <div className="flex justify-between items-start">
                                                <span className="text-xs font-medium text-gray-500 uppercase">Completion Date</span>
                                                <span className="text-sm text-gray-500 text-right">{renderCompletionDate(person.completionDate)}</span>
                                            </div>
                                        </div>
                                    </div>
                                ))}
                            </div>

                            {/* Desktop Table View */}
                            <table className="hidden sm:table min-w-full divide-y divide-gray-200">
                                <thead className="bg-white">
                                    <tr>
                                        <th scope="col" className="px-3 sm:px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Serial No.</th>
                                        <th scope="col" className="px-3 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Shareholder Name</th>
                                        <th scope="col" className="px-3 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Email</th>
                                        <th scope="col" className="px-3 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">KYC Status</th>
                                        <th scope="col" className="px-3 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Completion Date</th>
                                    </tr>
                                </thead>
                                <tbody className="bg-white divide-y divide-gray-200">
                                    {shareholdersToDisplay.length === 0 && !isLoading && (
                                        <tr><td colSpan={5} className="text-center px-6 py-4 text-sm text-gray-500 italic">No shareholders have been added.</td></tr>
                                    )}
                                    {shareholdersToDisplay.map((person, index) => (
                                        <tr key={person.id || index}>
                                            <td className="px-3 sm:px-4 py-4 whitespace-nowrap text-sm font-medium text-gray-500">{index + 1}</td>
                                            <td className="px-3 sm:px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{person.name}</td>
                                            <td className="px-3 sm:px-6 py-4 text-sm text-gray-500">
                                                <div className="flex flex-col space-y-1">
                                                    <span className="break-all">{person.email}</span>
                                                    {(person.email && person.email !== 'N/A' && person.kycVerificationStatus !== 'APPROVED' && person.kycVerificationStatus !== 'VERIFIED' && person.kycVerificationStatus !== 'REJECTED') && (
                                                        <button
                                                            onClick={() => handleResend(person.email, person.name)}
                                                            className="text-[#60c193] hover:text-green-400 text-xs font-medium hover:underline disabled:text-gray-400 disabled:cursor-not-allowed text-left w-fit"
                                                        >
                                                            Resend Link
                                                        </button>
                                                    )}
                                                </div>
                                            </td>
                                            <td className="px-3 sm:px-6 py-4 whitespace-nowrap text-sm text-gray-500">{renderStatus(person.kycVerificationStatus)}</td>
                                            <td className="px-3 sm:px-6 py-4 whitespace-nowrap text-sm text-gray-500">{renderCompletionDate(person.completionDate)}</td>
                                        </tr>
                                    ))}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            {/* Action Buttons */}
            <div className="flex flex-col sm:flex-row justify-center items-center mt-6 sm:mt-8 gap-3 sm:gap-4 pb-6 sm:pb-8 px-4">
                <button
                    type="button"
                    onClick={onBack}
                    className="w-full sm:w-auto inline-flex items-center justify-center px-6 sm:px-8 py-3 border border-transparent font-medium rounded-md shadow-sm text-white bg-gray-400 hover:bg-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-400"
                >
                    Previous Step
                </button>
                <button
                    type="button"
                    onClick={handleFinish}
                    className={`w-full sm:w-auto inline-flex items-center justify-center px-6 sm:px-8 py-3 border border-transparent font-medium rounded-md shadow-sm text-white bg-[#004141] hover:bg-[#005a5a] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500}`}
                >
                    Next
                    <ArrowRightCircleIcon className="ml-2 -mr-1 h-5 w-5" aria-hidden="true" />
                </button>
            </div>

            {/* Modal for Video KYC Iframe */}
            {isModalOpen && (
                <div className="fixed inset-0 z-50 overflow-y-auto" aria-labelledby="modal-title" role="dialog" aria-modal="true">
                    <div className="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
                        <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" aria-hidden="true"></div>
                        <span className="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
                        <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle w-full max-w-xs sm:max-w-lg md:max-w-2xl lg:max-w-4xl mx-4">
                            <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                                <div className="sm:flex sm:items-start">
                                    <div className="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
                                        <h3 className="text-lg leading-6 font-medium text-gray-900" id="modal-title">Video KYC Verification</h3>
                                        <button
                                            type="button"
                                            className="absolute top-3 right-3 text-gray-400 hover:text-gray-500 focus:outline-none"
                                            onClick={handleCloseModal}
                                        >
                                            <span className="sr-only">Close</span>
                                            <XMarkIcon className="h-6 w-6" aria-hidden="true" />
                                        </button>
                                        <div className="mt-4 w-full h-[60vh] sm:h-[75vh] lg:h-[80vh]">
                                            {/* UPDATED Iframe */}
                                            {iframeSrc ? (
                                                <iframe
                                                    src={iframeSrc}
                                                    title="Video KYC Verification"
                                                    className="w-full h-full border-0"
                                                    allow="camera; microphone"
                                                    onError={(e) => {
                                                        console.error("Iframe loading error:", e);
                                                        setError("Failed to load verification session iframe.");
                                                        setIframeSrc('');
                                                        setIsModalOpen(false); /* Close modal on error */
                                                    }}
                                                ></iframe>
                                            ) : (
                                                <div className="flex flex-col justify-center items-center h-full text-gray-500">
                                                    <ArrowPathIcon className="animate-spin h-8 w-8 mb-2" />
                                                    <p className="text-sm sm:text-base">Loading verification session...</p>
                                                </div>
                                            )}
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                                <button
                                    type="button"
                                    className="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
                                    onClick={handleCloseModal}
                                >
                                    Close
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            )}
        </div>
    );
};

export default ShareholderKycStatus;