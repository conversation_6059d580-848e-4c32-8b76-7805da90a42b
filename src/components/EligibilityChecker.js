import React, { useState, useEffect } from 'react'; // Keep React imports as they were in your original file
import { InformationCircleIcon, XMarkIcon } from '@heroicons/react/24/outline';
// Import your SharedCache implementation
import SharedCache from '../sharedCache';
import config from "../config.json";
import { updateKyc, getKycInfo } from '../api/kyc';
import { ArrowPathIcon } from '@heroicons/react/20/solid'; // Import a spinner icon
import Modal from 'react-modal';

const EligibilityChecker = () => { // No React.FC type
    // --- State Variables ---
    const [currentPage, setCurrentPage] = useState('dashboard');
    const [isEligible, setIsEligible] = useState(null); // Keep null for initial state
    const [formSubmitted, setFormSubmitted] = useState(false);
    // Form field states
    const [location, setLocation] = useState('');
    const [businessAge, setBusinessAge] = useState('');
    const [crValidity, setCrValidity] = useState('');
    const [companyType, setCompanyType] = useState('');
    const [sector, setSector] = useState('');
    const [turnover, setTurnover] = useState('');
    const [wasPreFilled, setWasPreFilled] = useState(false); // <-- Add this state
    const [employees, setEmployees] = useState('');
    const [creditScore, setCreditScore] = useState('');
    const [isLoadingUser, setIsLoadingUser] = useState(true); // Keep this
    const [isLoadingKyc, setIsLoadingKyc] = useState(false); // Add state for loading existing KYC data
    const [isUpdatingKyc, setIsUpdatingKyc] = useState(false);
    // Phone number state (will be pre-filled)
    const [phoneNumber, setPhoneNumber] = useState('');
    // Loading state for async operations
    const [agreedAccurate, setAgreedAccurate] = useState(false);
    const [readTerms, setReadTerms] = useState(false);
    const [readFinancialConsent, setReadFinancialConsent] = useState(false);
    const openConsentModal = () => setIsConsentModalOpen(true);
    const closeConsentModal = () => setIsConsentModalOpen(false);
    const [isConsentModalOpen, setIsConsentModalOpen] = useState(false);
    const [isSubmitting, setIsSubmitting] = useState(false); // New state for submission loading

    // Effect 1: Fetch user data and existing KYC on component mount
    useEffect(() => {
        const fetchInitialData = async () => {
            console.log("EligibilityChecker mounted. Fetching initial data...");
            setIsLoadingUser(true); // Indicate loading basic user info (like phone)
            setIsLoadingKyc(true);  // Indicate loading existing KYC form data

            let kycResult = null; // Define kycResult outside the inner try/catch to access mobileNo later

            try {
                // 1. Get user data from SharedCache
                const cachedUser = SharedCache.get("user");
                const userId = cachedUser?._id || cachedUser?.id;

                if (userId) {
                    console.log(`User ID found: ${userId}. Fetching details...`);

                    // 2. Fetch existing KYC data using getKycInfo
                    try {
                        console.log("Attempting to fetch existing KYC info...");
                        kycResult = await getKycInfo(userId); // Assign to the outer variable
                        console.log("getKycInfo result:", kycResult);

                        // Check if KYC data and the nested businessDetails exist
                        if (kycResult.success && kycResult.user?.kyc?.businessDetails) {
                            const businessDetails = kycResult.user.kyc.businessDetails; // Access the nested object
                            console.log("Existing Business Details found:", businessDetails);

                            // *** START: Added Check for Pre-filled Status ***
                            // Check if ALL 7 required fields have values in the fetched data
                            const allFieldsPresent =
                                (businessDetails.isQatarBased !== undefined && businessDetails.isQatarBased !== null) &&
                                !!businessDetails.businessAge &&
                                !!businessDetails.crValidity &&
                                !!businessDetails.companyType &&
                                !!businessDetails.sector &&
                                !!businessDetails.turnover &&
                                !!businessDetails.employees;

                            if (allFieldsPresent) {
                                console.log("All 7 required fields were found in fetched KYC data.");
                                setWasPreFilled(true);
                            } else {
                                console.log("Fetched KYC data did not contain all 7 required fields.");
                                setWasPreFilled(false); // Ensure it's false if not all fields are present
                            }
                            // *** END: Added Check for Pre-filled Status ***

                            // 3. Pre-fill form fields using data from businessDetails
                            if (businessDetails.isQatarBased !== undefined && businessDetails.isQatarBased !== null) {
                                const loc = businessDetails.isQatarBased ? 'Qatar' : 'Outside Qatar';
                                setLocation(loc);
                                console.log(`Pre-filling Location from businessDetails.isQatarBased: ${loc}`);
                            }
                            if (businessDetails.businessAge) {
                                setBusinessAge(businessDetails.businessAge);
                                console.log(`Pre-filling Business Age: ${businessDetails.businessAge}`);
                            }
                            if (businessDetails.crValidity) {
                                setCrValidity(businessDetails.crValidity);
                                console.log(`Pre-filling CR Validity: ${businessDetails.crValidity}`);
                            }
                            if (businessDetails.companyType) {
                                setCompanyType(businessDetails.companyType);
                                console.log(`Pre-filling Company Type: ${businessDetails.companyType}`);
                            }
                            if (businessDetails.sector) {
                                setSector(businessDetails.sector);
                                console.log(`Pre-filling Sector: ${businessDetails.sector}`);
                            }
                            if (businessDetails.turnover) {
                                setTurnover(businessDetails.turnover);
                                console.log(`Pre-filling Turnover: ${businessDetails.turnover}`);
                            }
                            if (businessDetails.employees) {
                                setEmployees(businessDetails.employees);
                                console.log(`Pre-filling Employees: ${businessDetails.employees}`);
                            }
                            // Pre-fill consents if they exist at the top level of kyc
                            // const kycTopLevel = kycResult.user.kyc;
                            // if (kycTopLevel.agreedAccurate) setAgreedAccurate(kycTopLevel.agreedAccurate);
                            // if (kycTopLevel.readTerms) setReadTerms(kycTopLevel.readTerms);
                            // if (kycTopLevel.readFinancialConsent) setReadFinancialConsent(kycTopLevel.readFinancialConsent);

                        } else {
                            console.log("No existing businessDetails found in KYC data or fetch failed, user might need to fill the form.");
                        }
                    } catch (kycError) {
                        console.error("Error fetching existing KYC data:", kycError);
                        // Don't block the rest of the flow if KYC fetch fails, just log it.
                    } finally {
                        setIsLoadingKyc(false); // Finish loading KYC part
                    }


                    // 4. Fetch/Set phone number
                    // Check if phone number came from the getKycInfo result first
                    const fetchedMobileNo = kycResult?.user?.mobileNo;
                    if (fetchedMobileNo) {
                        console.log(`Using phone number from getKycInfo result: ${fetchedMobileNo}`);
                        // Assuming format is correct or apply necessary formatting/stripping "+974"
                        // Example: setPhoneNumber(fetchedMobileNo.replace(/^\+?974/, ''));
                        setPhoneNumber(fetchedMobileNo);
                    } else {
                        // Fallback to original separate fetch if mobileNo wasn't in getKycInfo result
                        console.log("Phone number not found in getKycInfo result. Fetching basic user details separately...");
                        try {
                            const response = await fetch(`${config.apiUrl}/ops/invoiceFinancing/users/id/${userId}`);
                            if (response.ok) {
                                const userData = await response.json();
                                console.log("User data received from API for phone:", userData);
                                if (userData?.mobileNo) {
                                    console.log(`Pre-filling phone number from separate fetch: ${userData.mobileNo}`);
                                    setPhoneNumber(userData.mobileNo); // Or apply formatting/stripping
                                } else {
                                    console.log("Separate user data fetched, but no 'mobileNo' field found.");
                                }
                            } else {
                                console.error(`Failed to fetch basic user data: ${response.status} ${response.statusText}`);
                                // Handle API errors from separate fetch
                                try {
                                    const errorData = await response.json();
                                    console.error("API Error details (separate fetch):", errorData.message || errorData);
                                } catch (e) { /* Ignore if response body isn't JSON */ }
                            }
                        } catch (fetchError) {
                            console.error("Error during separate phone number fetch:", fetchError);
                        }
                    }

                } else {
                    console.log("User ID not found in SharedCache. Cannot fetch data.");
                    // If no user ID, no point trying to load KYC either
                    setIsLoadingKyc(false);
                }
            } catch (error) {
                console.error('Error during initial data fetch setup (e.g., getting user from cache):', error);
                // Ensure loading states are reset even if setup fails
                setIsLoadingKyc(false);
            } finally {
                setIsLoadingUser(false); // Finish loading basic user info/phone part
                // Note: setIsLoadingKyc should be false already from its specific try/finally or if user ID was missing
                console.log("Initial data fetch process complete.");
            }
        };

        fetchInitialData();
    }, []); // Empty dependency array ensures this runs only once on mount

    // Add this function within the EligibilityChecker component scope

    const sendEligibilityFailureEmail = async (msmeDetails, failedStatus, phoneFromState) => {
        // Assuming phoneFromState contains the 8-digit number and backend expects the full number with country code.
        // If phoneFromState might already include +974, add a check.
        let fullPhoneNumber = phoneFromState;
        if (phoneFromState && !phoneFromState.startsWith('+974')) {
            fullPhoneNumber = `+974${phoneFromState}`;
        } else if (!phoneFromState) {
            console.warn("Phone number is empty, cannot send eligibility failure email with phone.");
            // Decide if you want to proceed without a phone number or handle this case differently
            // For now, we'll send it as is, backend might handle missing phone.
        }

        console.log("Attempting to send eligibility failure email with data:", { msmeDetails, failedStatus, phone: fullPhoneNumber });

        try {
            const response = await fetch(`${config.apiUrl}/ops/invoiceFinancing/send-eligibility-failure-email`, { // Ensure config.apiUrl is correct
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    msmeDetails: msmeDetails, // Object with form field values
                    failedStatus: failedStatus,     // String describing the failure reason
                    phone: fullPhoneNumber          // User's phone number
                }),
            });

            const result = await response.json();

            if (response.ok && result.success) {
                console.log('Eligibility failure email sent successfully to support:', result.message);
                // You could add a small, non-intrusive notification here if desired,
                // but the main UI already informs them about the next steps.
            } else {
                console.error('Failed to send eligibility failure email to support:', result.message || `Status: ${response.status}`);
                // Optionally, display a subtle error to the user that the automated callback request might have failed.
            }
        } catch (error) {
            console.error('Error occurred while sending eligibility failure email:', error);
            // Optionally, display a subtle error.
        }
    };

    // Effect 2: Check eligibility based *only* on form inputs whenever they change
    useEffect(() => {
        // Check if all necessary fields have *some* value before calculating
        // This prevents calculating eligibility prematurely if fields are still loading/empty
        if (location && businessAge && crValidity && companyType && sector && turnover && employees) {
            console.log("Form fields populated. Checking eligibility based on current values...");

            // Basic knockout criteria check
            if (
                location === 'Outside Qatar' ||
                businessAge === '< 2 years' ||
                crValidity === '< 6 months'
                // Note: We already checked non-empty status above for companyType, sector, etc.
            ) {
                console.log("Eligibility check: FAILED (Basic Criteria)");
                setIsEligible(false);
            } else {
                // // Enterprise category check (Micro, Small, Medium)
                // const isMicroEnterprise = turnover === 'QAR <1Mn' && employees === '<10';
                // const isSmallEnterprise =
                //     turnover === 'QAR 1Mn - QAR 20Mn' && employees === '11-50';
                // const isMediumEnterprise =
                //     turnover === 'QAR 20Mn - 100Mn' && employees === '51-250';

                // console.log("Turnover selected:", turnover);
                // console.log("Employees selected:", employees);

                // if (isMicroEnterprise) {
                //     console.log("Micro-enterprise condition PASSED ✅");
                // } else if (isSmallEnterprise) {
                //     console.log("Small-enterprise condition PASSED ✅");
                // } else if (isMediumEnterprise) {
                //     console.log("Medium-enterprise condition PASSED ✅");
                // } else {
                //     console.log("Eligibility condition FAILED ❌ - Combination does not match any eligible enterprise category");
                // }

                // // Determine overall eligibility based on the category check
                // const overallEligibility = isMicroEnterprise || isSmallEnterprise || isMediumEnterprise;
                // console.log("Overall Eligibility based on category:", overallEligibility ? "✅ PASSED" : "❌ FAILED");
                // setIsEligible(overallEligibility);
                setIsEligible(true)
            }
        } else {
            // If not all fields are populated yet, reset eligibility or keep it null
            console.log("Not all required fields are populated yet. Eligibility not calculated.");
            // setIsEligible(null); // Optional: Reset if you want to clear previous state
        }
        // Removed formSubmitted from dependencies as it's only for revealing the result panel now
    }, [location, businessAge, crValidity, companyType, sector, turnover, employees, creditScore]); // Removed formSubmitted

    // Handle form submission
    const handleSubmit = async (e) => {
        e.preventDefault();
        if (validateForm() && !isUpdatingKyc) { // Prevent submission if already updating
            console.log("Form is valid. Preparing to update KYC...");
            setIsUpdatingKyc(true); // <-- Set loading state to true

            // 1. Get User ID
            const cachedUser = SharedCache.get("user");
            const userId = cachedUser?._id || cachedUser?.id;

            if (!userId) {
                console.error("handleSubmit: User ID not found. Cannot update KYC.");
                alert("User not logged in. Please log in to update KYC.");
                setIsUpdatingKyc(false); // <-- Reset loading state on early exit
                return;
            }

            // 2. Construct payload
            const kycPayload = {
                id: userId, // User identifier for the backend
                kyc: {      // Create a 'kyc' object at the root
                    businessDetails: { // Nest your businessDetails fields here
                        businessAge: businessAge,
                        crValidity: crValidity,
                        companyType: companyType,
                        sector: sector,
                        turnover: turnover,
                        employees: employees,
                        isQatarBased: location === 'Qatar',
                    }
                    // If you had other kyc.* fields to update, they would go here too
                    // e.g., qidNumber: 'somevalue' would be kyc: { qidNumber: 'somevalue', businessDetails: {...} }
                }
            };

            console.log("Attempting to update KYC with payload:", JSON.stringify(kycPayload, null, 2));

            try {
                // 3. Call updateKyc
                const updateResult = await updateKyc(kycPayload);



                // 4. Handle result
                if (updateResult.success) {
                    console.log("KYC updated successfully!");
                    // Update SharedCache if needed, maybe refetch kyc? Or assume updateResult contains updated data.
                    // Example: Refetch after successful update
                    // await getKycInfo(userId); // To refresh cache if getKycInfo updates it

                    setFormSubmitted(true); // Proceed to show results

                    // ----- MODIFICATION STARTS HERE -----
                    if (isEligible === false) { // Check the `isEligible` state
                        console.log("Eligibility check resulted in FAILED status. Sending callback request email to support.");

                        // Construct a detailed failure status for the email
                        let detailedFailedStatus = "Eligibility criteria not met after form submission."; // Default
                        if (location === 'Outside Qatar') {
                            detailedFailedStatus = "Business is located outside Qatar.";
                        } else if (businessAge === '< 2 years') {
                            detailedFailedStatus = "Business has been active for less than 2 years.";
                        } else if (crValidity === '< 6 months') {
                            detailedFailedStatus = "Commercial Registration validity is less than 6 months.";
                        } else {
                            // This part assumes if the above basic criteria passed, the failure is due to enterprise category
                            const isMicro = turnover === 'QAR <1Mn' && employees === '<10';
                            const isSmall = turnover === 'QAR 1Mn - QAR 20Mn' && employees === '11-50';
                            const isMedium = turnover === 'QAR 20Mn - 100Mn' && employees === '51-250';
                            if (!(isMicro || isSmall || isMedium)) {
                                detailedFailedStatus = "Business does not meet the required enterprise category (based on turnover and number of employees).";
                            }
                        }

                        const msmeDetailsForEmail = {
                            location: location,
                            businessAge: businessAge,
                            crValidity: crValidity,
                            companyType: companyType,
                            sector: sector,
                            turnover: turnover,
                            employees: employees,
                        };

                        // Call the function to send the email
                        // phoneNumber state should hold the user's current phone input
                        await sendEligibilityFailureEmail(msmeDetailsForEmail, detailedFailedStatus, phoneNumber);
                    }
                    // ----- MODIFICATION ENDS HERE -----

                } else {
                    console.error("Failed to update KYC:", updateResult.message);
                    alert(`Failed to update KYC: ${updateResult.message}`);
                    // Keep form active on failure, maybe? Or show specific error UI.
                    // setFormSubmitted(false); // Optional: Allow retry without reset
                }
            } catch (error) {
                console.error("Error during updateKyc call:", error);
                alert("An unexpected error occurred while updating your information. Please try again.");
            } finally {
                setIsUpdatingKyc(false); // <-- Reset loading state regardless of success/failure
            }

        } else {
            if (isUpdatingKyc) {
                console.warn("Submission attempt ignored: KYC update already in progress.");
            } else {
                console.warn("Form validation failed.");
                // Optionally trigger visual feedback for invalid fields
            }
        }
    };
    // Validate if all required fields are filled
    const validateForm = () => { // No return type annotation
        const isValid = !!(location && businessAge && crValidity && companyType &&
            sector && turnover && employees);
        return isValid;
    };

    // Reset form state
    const resetForm = () => {
        console.log("Resetting form...");
        setLocation('');
        setBusinessAge('');
        setCrValidity('');
        setCompanyType('');
        setSector('');
        setTurnover('');
        setEmployees('');
        setCreditScore('');
        // Do NOT reset phoneNumber here, keep the pre-filled value if it exists
        // setPhoneNumber('');
        setFormSubmitted(false);
        setIsEligible(null);
        // Note: We don't re-fetch user data on reset,
        // the pre-filled number from mount remains.
    };

    // --- Helper Functions ---

    // Function to render form fields (buttons or dropdown)
    const renderField = (label, value, setter, options, fieldType = 'buttons') => {
        if (fieldType === 'buttons' && Array.isArray(options)) {
            return (
                <div className="mb-5">
                    <div className="flex justify-between items-center">
                        <span className="font-semibold text-gray-700 text-sm min-w-[120px]">
                            {label}:
                        </span>
                        <div className="flex">
                            {options.map((option, index) => (
                                <button
                                    key={option}
                                    type="button"
                                    className={`px-4 py-2 text-sm font-medium transition-colors ${index === 0 ? 'rounded-l-md' : ''
                                        } ${index === options.length - 1 ? 'rounded-r-md' : ''
                                        } ${value === option
                                            ? 'bg-[#00393b] text-white'
                                            : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                                        } ${formSubmitted ? 'opacity-70 cursor-not-allowed' : 'cursor-pointer'
                                        }`}
                                    onClick={() => !formSubmitted && setter(option)}
                                    disabled={formSubmitted}
                                >
                                    {option}
                                </button>
                            ))}
                        </div>
                    </div>
                </div>
            );
        } else if (fieldType === 'dropdown') {
            return (
                <div className="mb-5">
                    <div className="relative">
                        <select
                            className={`w-full px-4 py-3 border border-gray-300 rounded-md text-sm bg-white appearance-none focus:outline-none focus:ring-2 focus:ring-[#00393b] focus:border-transparent ${formSubmitted ? 'bg-gray-50 cursor-not-allowed' : 'cursor-pointer'
                                }`}
                            value={value}
                            onChange={(e) => setter(e.target.value)}
                            disabled={formSubmitted}
                        >
                            <option value="">{`Select ${label.toLowerCase()}`}</option>
                            {options.map(option => (
                                <option key={option} value={option}>{option}</option>
                            ))}
                        </select>
                        {!formSubmitted && (
                            <div className="absolute inset-y-0 right-0 flex items-center px-3 pointer-events-none">
                                <svg className="w-4 h-4 text-gray-600 fill-current" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20">
                                    <path d="M9.293 12.95l.707.707L15.657 8l-1.414-1.414L10 10.828 5.757 6.586 4.343 8z" />
                                </svg>
                            </div>
                        )}
                    </div>
                </div>
            );
        }
        return null;
    };

    return (
        <>
            <div className="flex h-screen">
                <main className="flex-1 overflow-auto bg-gray-50">
                    <div className="p-8">
                        <h2 className="text-3xl text-black!important font-semibold mb-2 leading-tight">
                            Check your eligibility for invoice discounting product, instantly
                        </h2>
                        <p className="mb-8 text-gray-600 text-sm leading-relaxed">
                            Madad's eligibility checker is a valuable tool for MSMEs, allowing them to gauge the possibility to getting credit line with a bank or NBFC, specifically for discounting invoices on Madad's platform and getting cash quickly.
                        </p>

                        <div className="flex gap-6 flex-wrap lg:flex-nowrap">
                            {/* Left Panel - Form */}
                            <div className="flex-1 min-w-[650px] bg-[#edf5f7] p-8 rounded-xl shadow-lg">
                                <h3 className="text-lg font-semibold text-gray-800 mb-6">
                                    Fill this form to check eligibility
                                </h3>

                                <form onSubmit={handleSubmit}>
                                    {/* Two-column layout for form fields */}
                                    <div className="flex flex-col md:flex-row gap-x-6 mb-6">
                                        {/* Inner Left Column (Buttons) */}
                                        <div className="flex-1 mb-6 md:mb-0">
                                            {renderField('Location', location, setLocation, ['Qatar', 'Outside Qatar'], 'buttons')}
                                            {renderField('Active in business', businessAge, setBusinessAge, ['> 2 years', '< 2 years'], 'buttons')}
                                            {renderField('CR Validity', crValidity, setCrValidity, ['> 6 months', '< 6 months'], 'buttons')}
                                        </div>

                                        {/* Vertical Divider */}
                                        <div className="hidden md:block w-px bg-gray-300"></div>

                                        {/* Inner Right Column (Dropdowns) */}
                                        <div className="flex-1">
                                            <div className="mb-5">
                                                <label className="block text-sm font-medium text-gray-700 mb-2">
                                                    Company type
                                                </label>
                                                {renderField('Company type', companyType, setCompanyType, ['LLC', 'Corporation', 'Partnership', 'Sole Proprietorship', 'Other'], 'dropdown')}
                                            </div>

                                            <div className="mb-5">
                                                <label className="block text-sm font-medium text-gray-700 mb-2">
                                                    Sector
                                                </label>
                                                {renderField('Sector', sector, setSector, ['Trading', 'Construction', 'Real Estate', 'Manufacturing', 'Services', 'Hospitality', 'Other'], 'dropdown')}
                                            </div>

                                            <div className="mb-5">
                                                <label className="block text-sm font-medium text-gray-700 mb-2">
                                                    Turnover last year
                                                </label>
                                                {renderField('Turnover last year', turnover, setTurnover, ['QAR <1 million', 'QAR 1Mn - QAR 20Mn', 'QAR 20Mn - 100Mn', 'QAR >100 Mn'], 'dropdown')}
                                            </div>

                                            <div className="mb-8"> {/* Original had mb-8 for the last field group */}
                                                <label className="block text-sm font-medium text-gray-700 mb-2">
                                                    Number of employees
                                                </label>
                                                {renderField('Number of employees', employees, setEmployees, ['<10', '11-50', '51-250', '>250'], 'dropdown')}
                                            </div>
                                        </div>
                                    </div>

                                    {/* Results Section - only show when form is submitted (stays in left panel) */}
                                    {formSubmitted && (
                                        <div className={`mt-6 p-6 rounded-lg ${isEligible ? 'bg-white border border-gray-200' : 'border-red-500 bg-red-50'}`}>
                                            <div className="flex items-center mb-4">
                                                <div className="w-12 h-12 rounded-full flex items-center justify-center mr-4 bg-white border-2 border-green-500">
                                                    <svg className="w-6 h-6 stroke-green-500" fill="none" strokeWidth="3" viewBox="0 0 24 24">
                                                        {isEligible ? (
                                                            <path strokeLinecap="round" strokeLinejoin="round" d="M5 13l4 4L19 7" />
                                                        ) : (
                                                            <path strokeLinecap="round" strokeLinejoin="round" d="M6 18L18 6M6 6l12 12" />
                                                        )}
                                                    </svg>
                                                </div>
                                                <h3 className={`text-2xl font-bold ${isEligible ? 'text-green-500' : 'text-red-600'}`}>
                                                    {isEligible ? 'Your Business is eligible' : 'Your Business is not eligible'}
                                                </h3>
                                            </div>
                                            <p className="text-gray-600 text-base mb-6">
                                                {isEligible
                                                    ? 'You meet the basic criteria for a credit line against your due invoices.'
                                                    : 'Unfortunately, your business does not meet the current eligibility criteria.'
                                                }
                                            </p>
                                            {isEligible && (
                                                <div className="bg-[#edf5f7] p-6 rounded-lg">
                                                    <div className="flex items-center mb-4">
                                                        <h4 className="text-base font-semibold text-gray-800">
                                                            Declaration and consent
                                                        </h4>
                                                        <div className="ml-2 w-4 h-4 bg-gray-400 rounded-full flex items-center justify-center">
                                                            <span className="text-white text-xs font-bold">i</span>
                                                        </div>
                                                    </div>
                                                    <div className="space-y-4">
                                                        <label className="flex items-start text-base text-gray-700">
                                                            <input type="checkbox" className="mt-1 mr-3 h-4 w-4 text-green-500 border-gray-300 rounded focus:ring-green-500" />
                                                            <span>I confirm accuracy and completeness of all information provided.</span>
                                                        </label>
                                                        <label className="flex items-start text-base text-gray-700">
                                                            <input type="checkbox" className="mt-1 mr-3 h-4 w-4 text-green-500 border-gray-300 rounded focus:ring-green-500" />
                                                            I agree to the <a href="/terms-and-conditions" target="_blank" rel="noopener noreferrer" className="text-green-600 mx-1 underline hover:text-green-800">T&Cs</a> and <a href="/privacy-policy" target="_blank" rel="noopener noreferrer" className="text-green-600 mx-1 underline hover:text-green-800">Privacy Policy</a>.
                                                        </label>
                                                        <label className="flex items-start text-base text-gray-700">
                                                            <input type="checkbox" className="mt-1 mr-3 h-4 w-4 text-green-500 border-gray-300 rounded focus:ring-green-500" />
                                                            I agree to the <button type="button" onClick={openConsentModal} className="text-green-600 underline hover:text-green-800 ml-1">Financial Data & Credit Bureau Report Consent</button>.
                                                        </label>
                                                    </div>
                                                </div>
                                            )}
                                            <div className="justify-end flex gap-3 mt-6">
                                                <button
                                                    type="button"
                                                    className="px-6 py-3 bg-[#189e52] text-white text-base font-medium rounded-md hover:bg-green-600 transition-colors"
                                                    onClick={resetForm}
                                                >
                                                    Check again
                                                </button>
                                                <button
                                                    type="button"
                                                    onClick={() => { window.location.href = '/kyc/business-documents'; }} // Consider react-router navigation

                                                    className="px-6 py-3 bg-[#00413f] text-white text-base font-medium rounded-md hover:bg-teal-800 transition-colors"
                                                >
                                                    Apply Now
                                                </button>
                                                <a
                                                    type="button"
                                                    href="https://madadfintech.com/"
                                                    target="_blank"
                                                    rel="noopener noreferrer"
                                                    className="px-6 py-3 bg-gray-300 text-gray-700 text-base font-medium rounded-md hover:bg-gray-400 transition-colors"
                                                >
                                                    Know More
                                                </a>
                                            </div>
                                            {!isEligible && (
                                                <button
                                                    type="button"
                                                    className="mt-4 px-6 py-3 bg-green-500 text-white text-base font-medium rounded-md hover:bg-green-600 transition-colors"
                                                    onClick={resetForm}
                                                >
                                                    Check again
                                                </button>
                                            )}
                                        </div>
                                    )}

                                    {/* Submit button - only show when form is not submitted (stays in left panel) */}
                                    {!formSubmitted && (
                                        <div className="flex justify-end mt-8"> {/* Added mt-8 for spacing if results not shown */}
                                            <button
                                                type="submit"
                                                className={`px-6 py-2.5 text-sm font-semibold rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-opacity-50 flex items-center ${(!validateForm() || isUpdatingKyc)
                                                    ? 'bg-gray-400 text-white cursor-not-allowed'
                                                    : 'bg-[#00393b] text-white hover:bg-gray-900 focus:ring-gray-800'
                                                    }`}
                                                disabled={!validateForm() || isUpdatingKyc || isLoadingKyc || isLoadingUser}
                                            >
                                                {isUpdatingKyc ? (
                                                    <>
                                                        <ArrowPathIcon className="animate-spin h-5 w-5 mr-2" />
                                                        Checking...
                                                    </>
                                                ) : (
                                                    'Check Eligibility'
                                                )}
                                            </button>
                                        </div>
                                    )}
                                </form>
                            </div>

                            {/* Right Panel - Static Graphic and Banks Box */}
                            <div className="w-full lg:w-1/5 xl:w-1/3 space-y-6">
                                {/* Static Green Graphic Box */}
                                <div className="bg-green-500 p-8 rounded-xl shadow-lg flex flex-col text-white text-center min-h-[380px]"> {/* Adjusted min-height */}
                                    <div className="flex-grow flex flex-col items-center justify-center pt-4 pb-2">
                                        <div className="w-32 h-32 mb-6 flex items-center justify-center">
                                            <img src={require("..//images/rocket.png")} alt="Setup Account Graphic" className="w-28 h-28 sm:w-32 sm:h-32 mb-5" />
                                        </div>
                                        <h3 className="text-xl font-bold mb-2 uppercase tracking-wide">
                                            SETTING UP YOUR ACCOUNT IN 6 STEPS
                                        </h3>
                                        <p className="text-sm opacity-90 leading-relaxed px-4">
                                            Check Eligibility and setup your account with necessary document and get funds in no time.
                                        </p>
                                    </div>
                                </div>

                                {/* New Grey Box for Banks & NBFIs */}
                                {/* <div className="bg-gray-100 p-6 rounded-xl shadow-lg border border-gray-200">
                                    <p className="text-sm font-semibold text-gray-700 mb-4 text-center">
                                        Banks currently partnered with Madad
                                    </p>
                                    <div className="bg-white rounded-lg p-3 sm:p-4">
                                        <div className="flex justify-around items-center space-x-4 sm:space-x-6">
                                            <img
                                                src="/images/al_masraf_logo.png" // Placeholder for Al Masraf / QIB
                                                alt="Al Masraf QIB"
                                                className="h-10 sm:h-9 object-contain" // Adjusted height
                                                onError={(e) => { e.target.onerror = null; e.target.src = "https://i.ibb.co/9m2T5tyZ/image.png"; }}
                                            />
                                            <img
                                                src="/images/al_rayan_bank_logo.png" // Placeholder for Al Rayan Bank
                                                alt="Al Rayan Bank"
                                                className="h-10 sm:h-9 object-contain" // Adjusted height
                                                onError={(e) => { e.target.onerror = null; e.target.src = "https://i.ibb.co/27knBpL6/Al-Rayan-Bank-Logo.jpg"; }}
                                            />
                                        </div>
                                    </div>

                                </div> */}
                            </div>
                        </div>

                        <div className="mt-12 text-xs text-gray-500 text-center">
                            <p>Disclaimer*: The information provided is not guaranteed to be accurate or complete and is indicative and tentative in nature.</p>
                            <p>Madad bears no liability for updating the data or for any losses arising from the use of this information.</p>
                        </div>
                        <div className="mt-6 mb-12 text-center">
                            <p className="text-green-600 font-medium text-sm">
                                Madad is the first invoice discounting FinTech company in Qatar and a part of Qatar Central Bank's FinTech Sandbox
                            </p>
                        </div>
                        <Modal isOpen={isConsentModalOpen} onRequestClose={closeConsentModal} contentLabel="Financial Data Consent" style={{ overlay: { backgroundColor: 'rgba(0, 0, 0, 0.75)', zIndex: 1050 }, content: { top: '50%', left: '50%', transform: 'translate(-50%, -50%)', width: '90%', maxWidth: '700px', maxHeight: '80vh', padding: '0', border: 'none', borderRadius: '8px', overflow: 'hidden', display: 'flex', flexDirection: 'column' } }} ariaHideApp={false}>
                            <div className="flex justify-between items-center p-4 bg-gray-100 border-b rounded-t-lg flex-shrink-0"><h2 className="text-lg font-semibold">Financial Data & Credit Bureau Report Consent</h2><button onClick={closeConsentModal} className="text-gray-500 hover:text-gray-800"><XMarkIcon className="h-6 w-6" /></button></div>
                            <div className="p-6 prose max-w-none overflow-y-auto"> {/* Consent text */}
                                <div>
                                    <h1 className="text-2xl font-bold text-gray-800 mb-4">Consent Form - Credit Report</h1>

                                    <div className="mb-6">
                                        <h2 className="text-xl font-semibold text-gray-700 mb-2">First: By signing this consent I hereby agree to the following:</h2>
                                        <ol className="list-decimal pl-6 space-y-2">
                                            <li className="text-gray-700">
                                                Madad Financial Technologies, its partners and lenders will inquire about my credit history including returned checks. The inquiry may be made at any stage of my relationship with the Madad which could range from me requesting (but not yet granted) any service from Madad Financial Technologies, to me being the client of Madad Financial Technologies by way of established, approved relationship.
                                            </li>
                                            <li className="text-gray-700">
                                                Madad Financial Technologies, its partners and lenders will review all facilities granted to me, including the regularity of payment, and that Madad Financial Technologies employees need to review and discuss this information to perform their work in relation to the credit history, and that such credit history may affect Madad Financial Technologies' decision to grant the required facilities, either positively or negatively, and that the credit report request will appear in the credit history and that may affect the decision of any other authority with access to my credit information.
                                            </li>
                                            <li className="text-gray-700">
                                                I acknowledge that Madad Financial Technologies, its partners and lenders are required to provide my credit information to Qatar Credit Information Center and vice versa, and that this includes providing information about any defaults for any reason.
                                            </li>
                                            <li className="text-gray-700">
                                                Madad Financial Technologies, its partners and lenders may request a credit history report more than once as and when needed according to work requirements.
                                            </li>
                                            <li className="text-gray-700">
                                                Madad Financial Technologies, its partners and lenders has the right to request my credit report at any time should any facility be granted and as long as the facility is not fully repaid.
                                            </li>
                                            <li className="text-gray-700">
                                                Madad Financial Technologies, its partners and lenders and its employees are exempted from any liability related to the inquiry of my credit history.
                                            </li>
                                            <li className="text-gray-700">
                                                I undertake to inform Madad Financial Technologies, its partners and lenders of any significant financial obligation on the credit history not mentioned in the Qatar Credit Center Report.
                                            </li>
                                        </ol>
                                    </div>

                                    <div className="mb-6">
                                        <h2 className="text-xl font-semibold text-gray-700 mb-2">Second: I also acknowledge and agree to the following:</h2>
                                        <ol className="list-decimal pl-6">
                                            <li className="text-gray-700">
                                                I understand that all of the above applies to me, whether an individual, institution or company, and that my signature below represents me personally, or the entity I legally own, represent or authorized on their behalf, (whether the request for the credit history report is for a real person or legal entity).
                                            </li>
                                        </ol>
                                    </div>
                                </div>
                                <p> I confirm that I have read and understood the above and agree to the terms and conditions outlined herein. </p> </div> <div className="p-4 bg-gray-50 border-t flex justify-end rounded-b-lg flex-shrink-0"><button onClick={closeConsentModal} className="px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 text-sm font-medium">Close</button></div>
                        </Modal>
                    </div>
                </main>
            </div>
        </>
    );
};

export default EligibilityChecker;