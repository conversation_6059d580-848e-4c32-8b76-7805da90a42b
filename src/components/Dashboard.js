import React, { useEffect, useState, useContext } from 'react';
import { LayoutContext } from '../MainLayout'; // Adjust path to MainLayout.js
import DashboardOverview from './dashboard/DashboardOverview';
import InvoiceForm from './dashboard/InvoiceForm';
import MyInvoices from './dashboard/MyInvoices';
import LoanOffersPage from './dashboard/LoanOffersPage'; // Ensure this component exists
import SharedCache from '../sharedCache'; // Ensure this utility exists and works
import Profile from './dashboard/Profile';
import MerchantPaymentsDashboard from './dashboard/Payments';
import ActivationJourney from './dashboard/ActivationJourney';
import axios from 'axios';
import config from '../config'; // Ensure config file exists with apiUrl
import MyBuyersPage from './dashboard/MyBuyers';

import {
  ClockIcon,
  ExclamationCircleIcon,
  CheckCircleIcon,
  XCircleIcon,
  InformationCircleIcon,
  GiftIcon
} from '@heroicons/react/24/outline';

import briefcaseMoney from '../images/briefcase_money.png'; // Adjust path
import LoadingModal from './Reusable/Loading';

const LoadingIndicator = ({ fileName }) => {
  return (
    <LoadingModal
      message={fileName ? `Loading ${fileName}...` : "Please wait a moment while we load your journey data"}
    />
  );
};

const Dashboard = () => {
  const { currentPage, setCurrentPage: setMainLayoutPage } = useContext(LayoutContext);

  const [showForm, setShowForm] = useState(false);
  const [showActivationJourneyContent, setShowActivationJourneyContent] = useState(true);
  const [applicationStatus, setApplicationStatus] = useState({ message: '', label: '', color: '', icon: null });
  const [loading, setLoading] = useState(true);
  const [userId, setUserId] = useState(() => (SharedCache.get('user') || {})._id || "");
  const [creditLineIsActive, setCreditLineIsActive] = useState(false);
  const [creditLine, setCreditLine] = useState({})
  const [offers, setOffers] = useState({})
  // Inside the Dashboard component, near other useState hooks:
  useEffect(() => {
    const handleCacheChange = () => {
      const cachedUser = SharedCache.get('user') || {};
      if (cachedUser._id && cachedUser._id !== userId) {
        setUserId(cachedUser._id);
      }
    };
    handleCacheChange(); // Check on mount
    // If SharedCache emits events:
    // SharedCache.on('change', handleCacheChange);
    // return () => SharedCache.off('change', handleCacheChange);
  }, []); // userId dependency removed to avoid potential loops if SharedCache is synchronous

  useEffect(() => {
    if (!userId) {
      const userFromCache = SharedCache.get('user');
      if (userFromCache && userFromCache._id) {
        setUserId(userFromCache._id) || setUserId(userFromCache.id);
      } else {
        setLoading(false); // Stop loading before redirect
        window.location.href = '/login'; // Consider history.push('/login') if using React Router for login page
      }
      return; // Exit effect if userId was missing
    }

    const fetchDataAndUpdateStatus = async () => {
      setLoading(true);
      let currentUserDetails;
      let authToken;
      let kycRedirect = false;

      try {
        const userResponse = await axios.get(`${config.apiUrl}/ops/invoiceFinancing/users/id/${userId}`);
        currentUserDetails = userResponse.data;
        SharedCache.set('user', currentUserDetails); // Update cache

        try {
          const creditLineResponse = await axios.get(
            `${config.apiUrl}/ops/invoiceFinancing/fetchCreditLine/${userId}`,
          );
          console.log(creditLineResponse.data?.creditLineStatus, "iasdiajid")
          setCreditLine(creditLineResponse.data)
          if (creditLineResponse.data?.creditLineStatus === 'ACTIVE') {
            setCreditLineIsActive(true);
            setShowActivationJourneyContent(false);
          } else {
            setCreditLineIsActive(false);
            setShowActivationJourneyContent(true);
          }

        } catch (creditLineError) {
          console.error("Failed to fetch credit line status:", creditLineError);
          setShowActivationJourneyContent(true); // Default to ActivationJourney on error
        }

        if (!kycRedirect && authToken && verificationStatus === "APPROVED") {
          try {
            const offersResponse = await axios.get(`${config.apiUrl}/ops/invoiceFinancing/offers/${userId}`, { headers: { 'x-auth-token': authToken } });
            if (offersResponse.data?.offers?.length > 0) {
              setOffers(offers)
              setApplicationStatus(prevStatus => ({ // Keep previous status label but update message
                ...prevStatus,
                message: `${prevStatus.message} You have new credit offers! Check Lender Offers.`,
                icon: GiftIcon, // Override icon
                label: "Credit Offer Received", // Optionally change label
              }));
              // if you have a 'lender-offers' page in sidebar: setMainLayoutPage('lender-offers');
            }
          } catch (offersError) {
            console.error("Failed to fetch offers:", offersError);
          }
        }

        const verificationStatus = currentUserDetails?.kyc?.verificationStatus;
        switch (creditLine.creditLineStatus) {
          case "INITIATED": case "REINITIATED":
            setApplicationStatus({ message: "Your credit line application journey has been initiated. Please complete the KYC process.", label: "Initiated", color: "bg-blue-100 text-blue-700", icon: ExclamationCircleIcon });
            kycRedirect = true;
            break;
          case "UNDER_REVIEW":
            setApplicationStatus({ message: "Your credit line application is currently under review. We'll update you soon.", label: "Under Review", color: "bg-yellow-100 text-yellow-700", icon: ClockIcon });
            break;
          case "INFO_NEEDED":
            setApplicationStatus({ message: "Additional information is required for your application. Please check your profile.", label: "Info Needed", color: "bg-orange-100 text-orange-700", icon: InformationCircleIcon });
            break;
          case "APPROVED":
            setApplicationStatus({ message: "Your credit line application is currently under review. We'll update you soon.", label: "Under Review", color: "bg-yellow-100 text-yellow-700", icon: ClockIcon });
            break;
          case "REJECTED":
            setApplicationStatus({ message: "Unfortunately, your credit line application was rejected. Please contact support.", label: "Rejected", color: "bg-red-100 text-red-700", icon: XCircleIcon });
            break;
          default:
            setApplicationStatus({ message: "Your credit line application is currently under review. We'll update you soon.", label: "Under Review", color: "bg-yellow-100 text-yellow-700", icon: ClockIcon });
        }

        if (kycRedirect) {
          setLoading(false);
          window.location.href = '/kyc'; // Or history.push('/kyc')
          return; // Stop further execution
        }

      } catch (error) {
        console.error("Failed to fetch user data and related statuses:", error);
        setApplicationStatus({ message: "Could not load dashboard data. Please try again later.", label: "Error", color: "bg-red-100 text-red-700", icon: XCircleIcon });
        setShowActivationJourneyContent(true); // Default on major error
        if (error.response && (error.response.status === 401 || error.response.status === 403)) {
          window.location.href = '/login'; // Unauthorized
        }
      } finally {
        if (!kycRedirect) { // Only set loading to false if not redirecting
          setLoading(false);
        }
      }
    };

    fetchDataAndUpdateStatus();
  }, [userId]); // Effect runs when userId changes

  const StatusIconToRender = applicationStatus.icon; // Changed variable name

  if (loading) {
    return <LoadingIndicator fileName={userId ? "dashboard data" : "user session"} />;
  }

  const renderDynamicContent = () => {
    if (showActivationJourneyContent) {
      return <ActivationJourney />;
    }
    if (showForm) { // Show InvoiceForm if requested, potentially on top of other content or replacing it
      return <InvoiceForm userId={userId} onClose={() => setShowForm(false)} />;
    }

    switch (currentPage) { // currentPage from LayoutContext
      case 'my-buyers':
        return <MyBuyersPage />;
      case 'my-invoices':
        return <MyInvoices />;
      case 'payments':
        return <MerchantPaymentsDashboard />;
      case 'settings':
        return <Profile />;
      case 'lender-offers': // Example if you add this page
        return <LoanOffersPage />;
      case 'dashboard':
      default:
        return <DashboardOverview setCurrentPage={setMainLayoutPage} setShowInvoiceForm={setShowForm} />;
    }
  };

  return (
    <div className="flex flex-col h-full"> {/* Fills the main content area of MainLayout */}
      {!creditLineIsActive && applicationStatus.message && (
        <div className={`p-3 flex items-center justify-center text-sm text-center sticky top-0 z-10 ${applicationStatus.color || 'bg-gray-100 text-gray-700'}`}>
          {StatusIconToRender && <StatusIconToRender className="h-5 w-5 mr-2 flex-shrink-0" />}
          <span className="truncate">{applicationStatus.message}</span>
        </div>
      )}

      <div className="flex-1 overflow-y-auto p-4 md:p-6"> {/* Content area with padding and scroll */}
        {currentPage === 'dashboard' && !showForm ? (
          showActivationJourneyContent ? (
            <div className="flex flex-col md:flex-row gap-4 md:gap-6">
              {/* Only show left column if credit line is NOT ACTIVE */}
              {!creditLineIsActive && (
                <div className="w-full md:w-1/3 lg:w-1/4 space-y-4 md:space-y-6">
                  <div className="bg-white rounded-lg shadow p-4 md:p-6">
                    <div className="flex flex-wrap items-center justify-between mb-3 md:mb-4">
                      <h2 className="text-md md:text-lg font-semibold mr-2">Current Status</h2>
                      <span className={`text-xs font-medium px-2 py-1 rounded-full flex items-center flex-shrink-0 ${applicationStatus.color || 'bg-gray-200 text-gray-800'}`}>
                        {StatusIconToRender && <StatusIconToRender className="h-3 w-3 mr-1" />}
                        <span className="truncate">{applicationStatus.label || 'N/A'}</span>
                      </span>
                    </div>
                    <p className="text-gray-600 text-sm break-words">
                      {applicationStatus.message}
                    </p>
                  </div>
                  <div className="bg-[#27c686] text-white rounded-lg shadow p-4 md:p-6">
                    <div className="flex items-center justify-center md:justify-start mb-3 md:mb-4 md:pl-4">
                      <img src={briefcaseMoney} alt="Briefcase with money" className="h-24 md:h-32 w-auto" />
                    </div>
                    <h2 className="text-lg md:text-xl font-bold mb-2 text-center md:text-left">Instant cash from your invoices</h2>
                    <p className="text-sm text-center md:text-left">
                      Turn your unpaid invoices into immediate working capital. Access funds in a few clicks—no waiting, no hassle.
                    </p>
                  </div>
                </div>
              )}
              <div className={`w-full ${!creditLineIsActive ? 'md:w-2/3 lg:w-3/4' : ''}`}>
                {renderDynamicContent()}
              </div>
            </div>
          ) : (
            renderDynamicContent()
          )
        ) : (
          renderDynamicContent()
        )}
      </div>
    </div>
  );
};

export default Dashboard;