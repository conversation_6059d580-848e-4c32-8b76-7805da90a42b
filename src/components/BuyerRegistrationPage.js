import React, { useState, useEffect } from 'react';
import { useHistory } from 'react-router-dom'; // Using useHistory for navigation
import { toast } from 'react-toastify'; // Assuming react-toastify is configured
import { useUser } from '../contexts/UserContext'; // To set userType globally in context (as per previous instructions)
import SharedCache from '../sharedCache';
// Ensure your config.json is accessible at this path
import config from "../config.json";
import LoadingModal from './Reusable/Loading';

export default function BuyerRegistrationPage() {
    const history = useHistory(); // Use useHistory for navigation
    const { userType, setUserType } = useUser(); // Get the setUserType function from context

    // State for form fields
    const [displayBuyerName, setDisplayBuyerName] = useState(''); // For display from initial link
    const [email, setEmail] = useState(''); // Stores the PLAIN, DECODED buyer email for form submission
    const [password, setPassword] = useState('');
    const [confirmPassword, setConfirmPassword] = useState('');

    // State for page status
    const [isLoading, setIsLoading] = useState(true); // True while verifying link or submitting form
    const [isEligible, setIsEligible] = useState(false); // True if link is valid and buyer can register
    const [error, setError] = useState(''); // Stores error messages
    const [successMessage, setSuccessMessage] = useState(''); // Stores success message after registration

    // Dummy messages for the right panel (matching the provided login UI)
    const messages = [
        {
            title: 'Welcome, Valued Buyer!',
            text: 'Streamline your invoice management and payment approvals.',
        },
        {
            title: 'Effortless Supplier Payments',
            text: 'Manage and approve invoices from your MSME suppliers with ease.',
        },
        {
            title: 'Empower Your Supply Chain',
            text: 'Provide liquidity to your MSME partners and strengthen relationships.',
        },
    ];
    const [index, setIndex] = useState(0); // For animating messages

    useEffect(() => {
        // This effect runs once to verify the link parameters
        const urlParams = new URLSearchParams(history.location.search);
        const queryName = urlParams.get('name');
        const queryBuyerEmailFromLink = urlParams.get('buyerEmail');
        const queryMsmeEmailFromLink = urlParams.get('msmeEmail');

        if (queryName) {
            try {
                setDisplayBuyerName(decodeURIComponent(queryName));
            } catch (e) {
                console.warn("Could not decode buyer name from query:", queryName, e);
            }
        }

        if (!queryBuyerEmailFromLink || !queryMsmeEmailFromLink) {
            setError("Invalid registration link. Required information is missing.");
            setIsLoading(false);
            return;
        }

        let plainBuyerEmail = '';
        let plainMsmeEmail = '';

        try {
            let tempBuyerEmail = queryBuyerEmailFromLink;
            for (let i = 0; i < 2; i++) {
                const decoded = decodeURIComponent(tempBuyerEmail);
                if (decoded === tempBuyerEmail) break;
                tempBuyerEmail = decoded;
            }
            plainBuyerEmail = tempBuyerEmail;

            let tempMsmeEmail = queryMsmeEmailFromLink;
            for (let i = 0; i < 2; i++) {
                const decoded = decodeURIComponent(tempMsmeEmail);
                if (decoded === tempMsmeEmail) break;
                tempMsmeEmail = decoded;
            }
            plainMsmeEmail = tempMsmeEmail;

        } catch (e) {
            console.error("Error decoding email parameters from link:", e);
            setError("Invalid format in registration link parameters. Please check the link.");
            setIsLoading(false);
            return;
        }

        setEmail(plainBuyerEmail); // Set state with the fully decoded email for form input and submission

        const verifyEligibility = async () => {
            setIsLoading(true);
            setError('');
            try {
                const encodedBuyerEmailForAPI = encodeURIComponent(plainBuyerEmail);
                const encodedMsmeEmailForAPI = encodeURIComponent(plainMsmeEmail);

                // This is the first API call from your original code
                const response = await fetch(
                    `${config.apiUrl}/ops/invoiceFinancing/verify-buyer-for-registration?buyerEmail=${encodedBuyerEmailForAPI}&msmeEmail=${encodedMsmeEmailForAPI}`
                );

                if (!response.ok) {
                    const errorText = await response.text();
                    throw new Error(`Network response was not ok: ${response.status} ${response.statusText}. Details: ${errorText}`);
                }

                const data = await response.json();

                if (data.success) {
                    setIsEligible(true);
                    if (data.buyerName && !queryName) {
                        setDisplayBuyerName(data.buyerName);
                    }
                } else {
                    setError(data.message || "You are not eligible to register with this link. Please contact support.");
                    setIsEligible(false);
                }
            } catch (err) {
                console.error("Verification API error:", err);
                setError(err.message || "Could not verify registration eligibility. Please check the link or try again later.");
                setIsEligible(false);
            } finally {
                setIsLoading(false);
            }
        };

        verifyEligibility();

    }, [history.location.search]); // Depend on search params to re-run if link changes

    // Effect for animating messages in the right panel
    useEffect(() => {
        const interval = setInterval(() => {
            setIndex((prevIndex) => (prevIndex + 1) % messages.length);
        }, 5000); // Change message every 5 seconds
        return () => clearInterval(interval);
    }, [messages.length]);

    const handleSubmit = async (e) => {
        e.preventDefault();
        setError('');
        setSuccessMessage('');

        if (password.length < 8) {
            setError("Password must be at least 8 characters long.");
            return;
        }
        if (password !== confirmPassword) {
            setError("Passwords do not match.");
            return;
        }

        setIsLoading(true);
        try {
            const registrationPayload = {
                username: email,
                email: email,
                password: password,
                role: 'buyerAdmin',
                lenderName: displayBuyerName,
                userType: 'buyer' // Sending userType to backend for consistency
            };

            const response = await fetch(
                `${config.apiUrl}/ops/invoiceFinancing/superadmin/create`, // API to create the BuyerAdmin user
                {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(registrationPayload),
                }
            );
            const data = await response.json();

            if (response.ok && data.success) {
                setSuccessMessage("Your buyer admin account has been created successfully! Please login using your email and password in the email login section.");
                toast.success("Registration successful! Please login using your email and password.", {
                    autoClose: 15000, // 15 seconds
                });
                // IMPORTANT: Store the complete SuperAdmin user object as the "user" in SharedCache
                // and as "buyerFullData" in localStorage. This is the only place it's definitively set for buyer registration.
                console.log(data, "erwnidadadokasdo")
                SharedCache.set("buyerFullData", data.admin); // The primary logged-in user object
                if (data.token) {
                    SharedCache.set("token", data.token); // Store token
                }
                setUserType('buyer'); // Update global context state

                // This is the crucial line for buyerFullData persistence
                localStorage.setItem(SharedCache.buyerFullData, JSON.stringify(data.admin));

                // --- DEBUGGING LOGS ---
                console.log("---- PRE-REDIRECT AUTH STATE (BuyerRegistrationPage) ----");
                console.log("UserContext setUserType('buyer') was called.");
                console.log("SharedCache userType:", SharedCache.get("userType"));
                console.log("localStorage userType:", localStorage.getItem("userType"));
                console.log("SharedCache token:", SharedCache.get("token"));
                console.log("SharedCache buyerFullData:", SharedCache.get("buyerFullData") ? "EXISTS" : "MISSING");

                // If you imported them:
                // console.log("auth.js getUserType() check:", authGetUserType());
                // console.log("auth.js isAuthenticated() check:", checkAuthNow());
                // --- END DEBUGGING LOGS ---

                setTimeout(() => {
                    history.push('/login'); // Redirect to buyer dashboard
                }, 6000);
            } else {
                setError(data.message || `Registration failed. Status: ${response.status}`);
                toast.error(data.message || "Registration failed.");
            }
        } catch (err) {
            console.error('Registration submission error:', err);
            setError('Failed to connect to the server for registration. Please try again later.');
            toast.error("Network error during registration.");
        } finally {
            setIsLoading(false);
        }
    };

    const backdrop = require("../images/backdrop.png"); // Background image URL

    // --- Conditional Rendering ---

    if (isLoading) {
        return <LoadingModal message='Verifying registration link' />
    }

    // 2. Show success message if registration was successful
    if (successMessage) {
        return <LoadingModal title='Welcome to Madad!' message='You have been registered as a buyer! You will be redirected to login shortly. Please login using your email and password in the Email Address section.' />
    }

    // 3. Show error message if verification failed or link is invalid
    if (error) {
        return (
            <div className="min-h-screen flex items-center justify-center bg-gray-100">
                <div className="bg-white p-8 rounded-lg shadow-md w-96 text-center">
                    <h1 className="text-2xl font-bold mb-4 text-red-600">Registration Problem</h1>
                    <p className="text-gray-700 mb-6">{error}</p>
                    <button
                        onClick={() => history.push('/')} // Go to login page
                        className="text-blue-600 hover:underline"
                    >
                        Go to Login Page
                    </button>
                </div>
            </div>
        );
    }

    // 4. If eligible, show the registration form matching the provided UI
    if (isEligible) {
        return (
            <div className="flex flex-col md:flex-row min-h-screen">
                {/* Left Panel (Form) */}
                <div className="w-full md:w-1/2 flex justify-center items-center p-6 sm:p-8 order-2 md:order-1">
                    <div className="w-full max-w-md">
                        <div className="flex flex-col items-center w-full">
                            <div className="flex flex-col items-center w-full max-w-[374px]">
                                {/* Logo */}
                                <img
                                    loading="lazy"
                                    src={require("../images/logo.jpg")} // Adjust path if necessary
                                    className="w-[120px] mb-6"
                                    alt="Madad Fintech Logo"
                                />

                                {/* Title */}
                                <div className="text-xl font-medium text-[#333333] mb-8 text-center">
                                    Register as Buyer Admin
                                </div>

                                <div className="w-full space-y-6 bg-[#f1f6f7] rounded-lg p-6 shadow-sm">
                                    <form onSubmit={handleSubmit} className="space-y-4">
                                        {displayBuyerName && (
                                            <div className="flex flex-col space-y-2">
                                                <label htmlFor="buyerName" className="text-sm text-green-600">
                                                    Company Name
                                                </label>
                                                <input
                                                    type="text"
                                                    id="buyerName"
                                                    value={displayBuyerName}
                                                    className="w-full py-3 px-4 text-base text-[#004141] bg-white rounded-md border border-gray-300 focus:ring-2 focus:ring-[#208039]/20 focus:border-[#208039] transition-all duration-200 outline-none"
                                                    readOnly
                                                />
                                            </div>
                                        )}
                                        <div className="flex flex-col space-y-2">
                                            <label htmlFor="email" className="text-sm text-green-600">
                                                Email (Username)
                                            </label>
                                            <input
                                                type="email"
                                                id="email"
                                                value={email}
                                                className="w-full py-3 px-4 text-base text-[#004141] bg-white rounded-md border border-gray-300 focus:ring-2 focus:ring-[#208039]/20 focus:border-[#208039] transition-all duration-200 outline-none"
                                                readOnly
                                                required
                                            />
                                        </div>
                                        <div className="flex flex-col space-y-2">
                                            <label htmlFor="password" className="text-sm text-gray-700">
                                                Create Password
                                            </label>
                                            <input
                                                type="password"
                                                id="password"
                                                value={password}
                                                onChange={(e) => setPassword(e.target.value)}
                                                className="w-full py-3 px-4 text-base text-[#004141] bg-white rounded-md border border-gray-300 focus:ring-2 focus:ring-[#208039]/20 focus:border-[#208039] transition-all duration-200 outline-none"
                                                placeholder="Enter new password (min 8 characters)"
                                                required
                                                minLength={8}
                                            />
                                        </div>
                                        <div className="flex flex-col space-y-2">
                                            <label htmlFor="confirmPassword" className="text-sm text-gray-700">
                                                Confirm Password
                                            </label>
                                            <input
                                                type="password"
                                                id="confirmPassword"
                                                value={confirmPassword}
                                                onChange={(e) => setConfirmPassword(e.target.value)}
                                                className="w-full py-3 px-4 text-base text-[#004141] bg-white rounded-md border border-gray-300 focus:ring-2 focus:ring-[#208039]/20 focus:border-[#208039] transition-all duration-200 outline-none"
                                                placeholder="Confirm new password"
                                                required
                                                minLength={8}
                                            />
                                        </div>

                                        {error && <div className="text-red-500 text-sm text-center">{error}</div>}

                                        <button
                                            type="submit"
                                            className={`w-full py-2.5 text-lg font-medium rounded-md transition-all duration-200 ${email && password && confirmPassword && password === confirmPassword && !isLoading
                                                ? "text-white bg-[#004141] shadow-lg hover:bg-[#003030]"
                                                : "text-gray-400 bg-gray-200 cursor-not-allowed"
                                                }`}
                                            disabled={isLoading || !email || !password || !confirmPassword || password !== confirmPassword || password.length < 8}
                                        >
                                            {isLoading ? 'Registering...' : 'Create Account'}
                                        </button>

                                        <div className="text-center text-sm mt-4">
                                            <span className="text-gray-600">Already have an account? </span>
                                            <button
                                                onClick={() => history.push('/')}
                                                className="text-[#208039] font-medium hover:underline cursor-pointer"
                                            >
                                                Log in
                                            </button>
                                        </div>
                                        <div className="text-center text-xs text-gray-500 mt-4">
                                            <p>By clicking continue, you agree to our <span className="text-blue-600 hover:underline cursor-pointer">Terms of Service</span> and <span className="text-blue-600 hover:underline cursor-pointer">Privacy Policy</span></p>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                {/* Right Panel (Image and Text) */}
                <div
                    className="hidden md:flex w-full md:w-1/2 bg-cover bg-center relative flex-col justify-end items-start p-8 sm:p-12 text-white order-1 md:order-2 min-h-[300px] md:min-h-screen transition-all duration-700 ease-in-out overflow-hidden"
                    style={{ backgroundImage: `url(${backdrop})` }}
                >
                    <div className="absolute inset-0 bg-black opacity-40"></div>

                    {/* Animated Text Block with Slide Animation */}
                    <div className="relative z-10 mb-10 w-full">
                        <div
                            key={index}
                            className="transform transition-all duration-500 ease-in-out"
                            style={{
                                transform: 'translateX(0)',
                                opacity: 1,
                                animation: 'slideIn 0.5s ease-in-out forwards'
                            }}
                        >
                            <h2
                                className="text-3xl lg:text-4xl font-bold mb-3 leading-tight"
                                style={{
                                    transform: 'translateX(0)',
                                    opacity: 1,
                                    transition: 'all 0.5s ease-in-out'
                                }}
                            >
                                {messages[index].title}
                            </h2>
                            <p
                                className="text-base lg:text-lg"
                                style={{
                                    transform: 'translateX(0)',
                                    opacity: 1,
                                    transition: 'all 0.5s ease-in-out 0.1s'
                                }}
                            >
                                {messages[index].text}
                            </p>
                        </div>
                    </div>

                    {/* Animated Dots */}
                    <div className="absolute bottom-10 left-8 sm:left-12 z-10 flex space-x-2">
                        {messages.map((_, i) => (
                            <span
                                key={i}
                                className={`block w-2 h-2 sm:w-2.5 sm:h-2.5 bg-white rounded-full transition-all duration-300 transform ${i === index
                                    ? 'opacity-100 scale-110'
                                    : 'opacity-50 scale-100 hover:opacity-75'
                                    }`}
                                style={{
                                    transform: i === index ? 'scale(1.1)' : 'scale(1)',
                                    opacity: i === index ? 1 : 0.5
                                }}
                            />
                        ))}
                    </div>

                    {/* Inline CSS Animation Keyframes */}
                    <style dangerouslySetInnerHTML={{
                        __html: `
                        @keyframes slideIn {
                            0% { transform: translateX(30px); opacity: 0; }
                            100% { transform: translateX(0); opacity: 1; }
                        }
                        @keyframes slideOut {
                            0% { transform: translateX(0); opacity: 1; }
                            100% { transform: translateX(-30px); opacity: 0; }
                        }
                        `
                    }} />
                </div>
            </div>
        );
    }
    return null;
}