import React, { useState, useEffect, useRef, useCallback } from 'react';
// Adjust API and Cache paths based on Profile.js location relative to these files
import SharedCache from '../../sharedCache'; // Assumed path from original Profile snippet
import config from '../../config.json'; // Assuming config is in the same relative location
import {
    getKycInfo,
    updateKyc,
    uploadKycDocument,
    updateShareholderTextData,
    uploadShareholderDocument,
    updateBuyers // Ensure this is exported from your API file
} from '../../api/kyc'; // Assumed path from original Profile snippet
import Modal from 'react-modal';
import { shortenDocumentName } from '../../components/utils';

// --- Import Heroicons ---
import {
    EyeIcon,             // View
    ArrowPathIcon,       // Replace Placeholder / Loading
    XMarkIcon,           // Close / Cancel
    CheckCircleIcon,     // Uploaded Check
    InformationCircleIcon, // Info/Consent (Might not be needed if consents removed)
    ExclamationTriangleIcon, // Error
    DocumentTextIcon,    // Generic Document
    UserCircleIcon,      // Person/User
    BuildingOffice2Icon, // Business
    UsersIcon,           // Shareholders/Buyers/Directors
    PencilSquareIcon,    // Edit
    CheckIcon,           // Save
    ArrowUpTrayIcon,     // Upload Trigger
    NoSymbolIcon,
    ClockIcon,
    XCircleIcon
} from '@heroicons/react/24/outline';
import LoadingModal from '../Reusable/Loading';

// --- Helper Functions ---
const getFileNameFromPath = (path) => {
    if (!path) return 'N/A';
    try {
        const decodedPath = decodeURIComponent(path);
        const filename = decodedPath.substring(decodedPath.lastIndexOf('/') + 1);
        return filename || (decodedPath.includes('/') ? 'N/A' : decodedPath) || 'N/A';
    } catch (e) {
        console.error("Error parsing filename:", path, e);
        return path || 'Invalid Name';
    }
};

const formatDate = (dateString) => {
    if (!dateString) return '-';
    const date = typeof dateString === 'string' ? new Date(dateString) : dateString;
    if (isNaN(date?.getTime()) || date?.getFullYear() <= 1970) return '-';
    try {
        return date.toLocaleDateString('en-GB', {
            day: '2-digit', month: 'short', year: 'numeric'
        }).replace(/ /g, '-');
    } catch (e) {
        console.error("Error formatting date:", dateString, e);
        return 'Invalid Date';
    }
};

const formatDateForInput = (dateString) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    if (isNaN(date.getTime())) return '';
    try {
        const year = date.getFullYear();
        const month = (date.getMonth() + 1).toString().padStart(2, '0');
        const day = date.getDate().toString().padStart(2, '0');
        return `${year}-${month}-${day}`;
    } catch (e) {
        console.error("Error formatting date for input:", dateString, e);
        return '';
    }
};

const deepClone = (obj) => {
    try {
        return JSON.parse(JSON.stringify(obj));
    } catch (e) {
        console.error("Deep clone error:", e);
        return null; // Return null or throw error, depending on desired handling
    }
};

const getNestedValue = (obj, path, defaultValue = '') => {
    if (!obj || !path) return defaultValue;
    const value = path.split('.').reduce((o, k) => o?.[k], obj);
    // Check for null explicitly, return defaultValue if null or undefined
    return (value === undefined || value === null) ? defaultValue : value;
};


// --- Profile Component (Replaces original Profile content) ---
const Profile = () => {
    const [userData, setUserData] = useState(null); // Stores the original fetched data
    const [formData, setFormData] = useState(null); // Stores the data being edited
    const [isLoading, setIsLoading] = useState(true);
    const [loadError, setLoadError] = useState('');
    const [isEditing, setIsEditing] = useState(false); // Renamed from editMode for consistency
    const [isSaving, setIsSaving] = useState(false);
    const [saveError, setSaveError] = useState('');
    const [uploadingDoc, setUploadingDoc] = useState({}); // Tracks upload status per unique key
    const [kycStatus, setKycStatus] = useState(null);

    const fileInputRefs = useRef({}); // Refs for file inputs

    // Modal State
    const [docViewerUrl, setDocViewerUrl] = useState('');
    const [isDocViewerOpen, setIsDocViewerOpen] = useState(false);
    // Consent modal state is removed as it's tied to submission

    // --- Document Structure Helper ---
    const ensureDocStructure = (doc) => {
        // Ensures a consistent object structure for document fields
        if (doc && typeof doc === 'object' && doc.filePath !== undefined) {
            return {
                filePath: doc.filePath || null,
                signedUrl: doc.signedUrl || null,
                uploadedOn: doc.uploadedOn, // Keep other fields if they exist
                mimeType: doc.mimeType,
                verificationStatus: doc.verificationStatus,
                verificationNotes: doc.verificationNotes,
                verifiedOrRejectedOn: doc.verifiedOrRejectedOn,
                verifiedOrRejectedBy: doc.verifiedOrRejectedBy,
            };
        } else if (typeof doc === 'string' && doc) {
            // Handle case where only a filePath string might be stored initially
            return { filePath: doc, signedUrl: null };
        }
        // Return a default empty structure if input is invalid or empty
        return { filePath: null, signedUrl: null };
    };

    // --- Data Fetching (Keep existing) ---
    const fetchData = useCallback(async (userId) => {
        setIsLoading(true);
        setLoadError('');
        try {
            console.log(`Workspaceing KYC info for review: userId=${userId}`); // Corrected log message
            const response = await getKycInfo(userId);
            console.log("getKycInfo Response:", response);

            if (response?.success && response?.user) {
                let data = response.user;

                if (response.user.kyc.verificationStatus) {
                    setKycStatus(response.user.kyc.verificationStatus);
                } else {
                    setKycStatus(null); // Or a default value
                }

                // Deep Defaults & Structure Enforcement
                data = data || {};
                data.kyc = data.kyc || {};
                data.kyc.businessDetails = data.kyc.businessDetails || {};
                data.kyc.employmentDetails = data.kyc.employmentDetails || {};
                data.kyc.incomeDetails = data.kyc.incomeDetails || {};
                data.shareholders = Array.isArray(data.shareholders) ? data.shareholders : [];
                // *** Ensure buyers array exists and has correct structure ***
                data.kyc.buyers = Array.isArray(data.kyc.buyers) ? data.kyc.buyers.map(b => ({
                    ...b,
                })) : [];
                data.kyc.directors = Array.isArray(data.kyc.directors) ? data.kyc.directors : [];
                data.authorizedSignatories = Array.isArray(data.authorizedSignatories) ? data.authorizedSignatories : [];
                data.beneficialOwners = Array.isArray(data.beneficialOwners) ? data.beneficialOwners : [];
                data.kyc.address = data.kyc.address || {};
                data.kyc.businessDetails = data.kyc.businessDetails || {};
                data.shareholders.forEach(sh => { sh.address = sh.address || {}; });

                // Document Defaults (Existing ones + shareholder docs)
                data.commercialRegistration = ensureDocStructure(data.commercialRegistration);
                data.tradeLicense = ensureDocStructure(data.tradeLicense);
                data.taxCard = ensureDocStructure(data.taxCard);
                data.establishmentCard = ensureDocStructure(data.establishmentCard);
                data.bankStatement = ensureDocStructure(data.bankStatement);
                data.auditedFinancialReport = ensureDocStructure(data.auditedFinancialReport);
                data.cashFlowLedger = ensureDocStructure(data.cashFlowLedger);
                data.commercialCreditReport = ensureDocStructure(data.commercialCreditReport);
                data.memorandumOfAssociation = ensureDocStructure(data.memorandumOfAssociation);
                data.articleOfAssociation = ensureDocStructure(data.articleOfAssociation);
                data.otherDocument = ensureDocStructure(data.otherDocument);
                data.otherDocumentTwo = ensureDocStructure(data.otherDocumentTwo);
                data.otherDocument3 = ensureDocStructure(data.otherDocument3);
                data.otherDocument4 = ensureDocStructure(data.otherDocument4);
                data.otherDocument5 = ensureDocStructure(data.otherDocument5);
                data.otherDocument6 = ensureDocStructure(data.otherDocument6);
                data.otherDocument7 = ensureDocStructure(data.otherDocument7);
                data.otherDocument8 = ensureDocStructure(data.otherDocument8);
                data.otherDocument9 = ensureDocStructure(data.otherDocument9);
                data.otherDocument10 = ensureDocStructure(data.otherDocument10);
                data.invoiceAdditionalDoc1 = ensureDocStructure(data.invoiceAdditionalDoc1);
                data.invoiceAdditionalDoc2 = ensureDocStructure(data.invoiceAdditionalDoc2);
                data.kyc.qatariId = ensureDocStructure(data.kyc.qatariId);
                data.kyc.passport = ensureDocStructure(data.kyc.passport);
                data.kyc.utilityBill = ensureDocStructure(data.kyc.utilityBill);
                if (!data.kyc.incomeDetails) data.kyc.incomeDetails = {};
                if (!data.kyc.employmentDetails) data.kyc.employmentDetails = {};
                data.kyc.incomeDetails.proofOfIncome = ensureDocStructure(data.kyc.incomeDetails.proofOfIncome);
                data.kyc.employmentDetails.employmentLetter = ensureDocStructure(data.kyc.employmentDetails.employmentLetter);
                data.shareholders.forEach(sh => {
                    sh.passport = ensureDocStructure(sh.passport);
                    sh.qid = ensureDocStructure(sh.qid);
                    sh.proofOfAddress = ensureDocStructure(sh.proofOfAddress);
                    sh.address = sh.address || {};
                });

                const clonedData = deepClone(data);
                if (clonedData) {
                    setUserData(clonedData);
                    setFormData(deepClone(clonedData));
                    console.log("User data set for review (structured):", clonedData);
                } else {
                    setLoadError("Failed to process user data after fetching.");
                    console.error("Deep cloning failed after structuring data.");
                }
            } else {
                setLoadError(response?.message || "Could not retrieve user data.");
            }
        } catch (error) {
            console.error("Error fetching data for review:", error);
            setLoadError(`Error loading data: ${error.message}. Please try again.`);
        } finally {
            setIsLoading(false);
        }
    }, []);
    useEffect(() => {
        const user = SharedCache.get('user') || {};
        const userId = user._id || user.id || "";

        if (!userId) {
            setLoadError("User ID not found. Please log in again.");
            setIsLoading(false); // Stop loading indicator
            return;
        }
        fetchData(userId);
    }, [fetchData]); // Depend on fetchData callback

    // --- Modal Handlers ---
    const openDocumentViewer = (url) => {
        if (!url) {
            alert("Document URL is not available or has expired.");
            return;
        }
        setDocViewerUrl(url);
        setIsDocViewerOpen(true);
    };
    const closeDocumentViewer = () => setIsDocViewerOpen(false);
    // Consent modal functions removed

    // --- Edit Mode Handlers ---
    const handleEditToggle = () => {
        if (isEditing) { // If cancelling
            handleCancelEdit();
        } else { // If entering edit mode
            const clonedUserData = deepClone(userData); // Start editing from the last saved state
            if (clonedUserData) {
                setFormData(clonedUserData);
                setSaveError(''); // Clear any previous save errors
                setIsEditing(true);
            } else {
                setLoadError("Failed to prepare data for editing. Please reload."); // Use loadError for this
                console.error("Deep cloning failed when entering edit mode.");
            }
        }
    };

    const handleCancelEdit = () => {
        const clonedUserData = deepClone(userData); // Revert form data to original state
        if (clonedUserData) {
            setFormData(clonedUserData);
        } else {
            // This is a more critical error, maybe force reload or show persistent error
            setLoadError("Failed to restore data. Please reload the page.");
            console.error("Deep cloning failed when cancelling edit.");
        }
        setIsEditing(false);
        setSaveError(''); // Clear save errors
        setUploadingDoc({}); // Reset upload indicators
    };

    // --- Input Change Handlers (Generic + Array specific) ---
    const handleInputChange = (path, value) => {
        setFormData(prevData => {
            const newData = deepClone(prevData);
            if (!newData) {
                console.error("Failed to clone state in handleInputChange");
                setSaveError("Error updating field. Please try again.");
                return prevData; // Return previous state to avoid crash
            }
            const keys = path.split('.');
            let current = newData;
            try {
                // Traverse path, creating objects if they don't exist
                for (let i = 0; i < keys.length - 1; i++) {
                    const key = keys[i];
                    // Check if the next key is an array index
                    const nextKeyIsIndex = /^\d+$/.test(keys[i + 1]);
                    if (current[key] === undefined || current[key] === null) {
                        // Create an empty array if the next key is an index, otherwise an object
                        current[key] = nextKeyIsIndex ? [] : {};
                    }
                    current = current[key];
                }
                // Set the final value
                current[keys[keys.length - 1]] = value;
                return newData;
            } catch (e) {
                console.error("Error setting value in handleInputChange:", path, value, e);
                setSaveError(`Error updating field: ${path}. Please try again.`);
                return prevData; // Return previous state on error
            }
        });
    };

    const handleArrayItemChange = (arrayPath, index, fieldPath, value) => {
        setFormData(prevData => {
            const newData = deepClone(prevData);
            if (!newData) {
                console.error("Failed to clone state in handleArrayItemChange");
                setSaveError("Error updating item. Please try again.");
                return prevData;
            }
            try {
                // Navigate to the target array using getNestedValue logic for safety
                const arrayKeys = arrayPath.split('.');
                let currentArrayLevel = newData;
                for (let i = 0; i < arrayKeys.length; i++) {
                    const key = arrayKeys[i];
                    if (currentArrayLevel[key] === undefined || currentArrayLevel[key] === null) {
                        // Create path if it doesn't exist
                        if (i < arrayKeys.length - 1) {
                            currentArrayLevel[key] = {};
                        } else {
                            currentArrayLevel[key] = []; // Create the array itself if missing
                        }
                        console.warn(`Created missing path segment: ${key} in ${arrayPath}`);
                    }
                    currentArrayLevel = currentArrayLevel[key];
                }
                const targetArray = currentArrayLevel;

                if (!Array.isArray(targetArray)) {
                    console.error(`Target path is not an array in handleArrayItemChange: path=${arrayPath}`);
                    setSaveError(`Data structure error for ${arrayPath}. Cannot update item.`);
                    return prevData;
                }

                // Ensure the item at index exists, creating it if necessary for nested updates
                if (index < 0) { // Basic bounds check
                    console.error(`Invalid negative index in handleArrayItemChange: index=${index}`);
                    return prevData;
                }
                // Allow adding implicitly if index equals length? No, let's require explicit add.
                if (index >= targetArray.length) {
                    console.error(`Index ${index} out of bounds for array ${arrayPath} (length ${targetArray.length})`);
                    setSaveError(`Cannot update item at index ${index} - item does not exist.`);
                    return prevData;
                }
                // Ensure the item itself is an object
                if (!targetArray[index] || typeof targetArray[index] !== 'object') {
                    targetArray[index] = {}; // Initialize item as object if missing or not an object
                    console.warn(`Initialized item at index ${index} in ${arrayPath}.`);
                }


                // Navigate within the item object using fieldPath
                const fieldKeys = fieldPath.split('.');
                let currentItemLevel = targetArray[index];

                for (let i = 0; i < fieldKeys.length - 1; i++) {
                    const key = fieldKeys[i];
                    if (currentItemLevel[key] === undefined || currentItemLevel[key] === null) {
                        currentItemLevel[key] = {}; // Create nested object path within the item if needed
                    }
                    currentItemLevel = currentItemLevel[key];
                }
                // Set the final value within the item
                currentItemLevel[fieldKeys[fieldKeys.length - 1]] = value;
                return newData; // Return the updated state
            } catch (e) {
                console.error("Error setting array item value:", arrayPath, index, fieldPath, value, e);
                setSaveError(`Error updating item: ${arrayPath}[${index}].${fieldPath}. Please try again.`);
                return prevData; // Return previous state on error
            }
        });
    };

    // --- Document Mapping (Defines API keys/paths for documents) ---
    const documentMappings = {
        // Root level docs
        commercialRegistration: "commercialRegistration",
        tradeLicense: 'tradeLicense',
        taxCard: 'taxCard',
        establishmentCard: 'establishmentCard',
        bankStatement: "bankStatement",
        auditedFinancialReport: 'auditedFinancialReport',
        cashFlowLedger: 'cashFlowLedger',
        commercialCreditReport: 'commercialCreditReport',
        memorandumOfAssociation: 'memorandumOfAssociation',
        articleOfAssociation: 'articleOfAssociation',
        otherDocument: 'otherDocument',
        otherDocumentTwo: 'otherDocumentTwo',
        otherDocument3: 'otherDocument3',
        otherDocument4: 'otherDocument4',
        otherDocument5: 'otherDocument5',
        otherDocument6: 'otherDocument6',
        otherDocument7: 'otherDocument7',
        otherDocument8: 'otherDocument8',
        otherDocument9: 'otherDocument9',
        otherDocument10: 'otherDocument10',
        invoiceAdditionalDoc1: "invoiceAdditionalDoc1", // Likely not relevant for profile
        invoiceAdditionalDoc2: "invoiceAdditionalDoc2", // Likely not relevant for profile
        // KYC level docs
        qatariId: "kyc.qatariId",
        passport: "kyc.passport",
        utilityBill: "kyc.utilityBill",
        // Nested KYC docs
        employmentLetter: "kyc.employmentDetails.employmentLetter",
        proofOfIncome: "kyc.incomeDetails.proofOfIncome",
        // Shareholder docs (Mapped for consistency, upload uses specific API)
        shareholderPassport: 'shareholders.passport',
        shareholderQid: 'shareholders.qid',
        shareholderProofOfAddress: 'shareholders.proofOfAddress',
        // Buyer docs (Mapped for consistency, upload needs specific API - currently read-only in UI)
        // buyerCompanyDocument: 'kyc.buyers.companyDocument'
    };

    // --- Document Upload/Replace Handler ---
    const handleFileReplace = async (documentType, file, index = null, label = 'Document') => {
        // documentType examples: 'commercialRegistration', 'kyc.qatariId', 'shareholderPassport', 'buyerCompanyDocument' (latter needs specific API call)
        // index is used for shareholder/buyer arrays
        if (!file) return;
        const userId = userData?._id || userData?.id;
        if (!userId) {
            setSaveError("User ID missing, cannot upload document.");
            return;
        }

        // Generate a unique key for tracking uploads, especially for array items
        const uploadKey = index !== null ? `${documentType}-${index}` : documentType;
        const isShareholderDoc = documentType.startsWith('shareholder');
        const isBuyerDoc = documentType.startsWith('buyer'); // Check if it's a buyer doc

        setUploadingDoc(prev => ({ ...prev, [uploadKey]: true })); // Set uploading status
        setSaveError(''); // Clear previous errors

        let finalUpdatedState = null; // To hold the state after successful update

        try {
            console.log(`Uploading document: userId=${userId}, type=${documentType}, index=${index}, file=${file.name}`);

            let response;
            if (isShareholderDoc) {
                // Extract the specific doc key ('passport', 'qid', 'proofOfAddress')
                const shareholderDocKey = documentType.split('.').pop(); // Assumes format 'shareholders.passport' etc.
                if (!['passport', 'qid', 'proofOfAddress'].includes(shareholderDocKey)) {
                    throw new Error(`Invalid shareholder document key: ${shareholderDocKey}`);
                }
                console.log(`Calling specific shareholder upload: index=${index}, key=${shareholderDocKey}`);
                response = await uploadShareholderDocument(userId, index, shareholderDocKey, file);
            } else if (isBuyerDoc) {
                // --- BUYER DOCUMENT UPLOAD ---
                // This needs a dedicated API call similar to uploadShareholderDocument
                // Assuming an API function `uploadBuyerDocument(userId, buyerIndex, file)` exists
                // const buyerDocKey = documentType.split('.').pop(); // e.g., 'companyDocument'
                console.error("Buyer document upload API call not implemented in this example.");
                setSaveError("Uploading buyer documents is not yet supported.");
                // Simulate failure for now:
                response = { success: false, message: "Buyer document upload not implemented." };
                // If implemented:
                // response = await uploadBuyerDocument(userId, index, file);
            } else {
                // --- General KYC Document Upload ---
                // We need the API key ('commercialRegistration', 'qatariId', etc.)
                // If documentType includes '.', it might be nested like 'kyc.qatariId'
                const apiKey = documentType.includes('.') ? documentType.split('.').pop() : documentType;
                // Check if the apiKey is valid for the general upload endpoint
                const validGeneralKeys = Object.keys(documentMappings).filter(k => !k.startsWith('shareholder') && !k.startsWith('buyer'));
                if (!validGeneralKeys.includes(apiKey)) {
                    // Might need to adjust logic if the API takes the full path vs just the key
                    console.warn(`Document type ${documentType} (key: ${apiKey}) might not be handled by general upload.`);
                    // Assuming API takes the key ('qatariId', 'passport' etc.)
                }
                response = await uploadKycDocument(userId, apiKey, file); // Pass the likely API key
            }

            // Process the response
            if (response?.success && response?.documentData) {
                console.log("Upload successful:", response);
                // Ensure the received document data has the expected structure
                const newDocData = ensureDocStructure({
                    filePath: response.documentData.filePath,
                    signedUrl: response.documentData.signedUrl,
                    uploadedOn: response.documentData.uploadedOn || new Date(), // Add timestamp if missing
                    mimeType: response.documentData.mimeType || file.type, // Add mimeType if missing
                });

                // Update formData state immediately for UI feedback
                setFormData(prevData => {
                    const newData = deepClone(prevData);
                    if (!newData) {
                        console.error("Failed to clone state after upload.");
                        setSaveError("Failed to update UI after upload. Please save and reload.");
                        finalUpdatedState = null; // Mark state update as failed
                        return prevData;
                    }

                    try {
                        let current = newData;
                        // Determine the correct path in the state object
                        let path;
                        if (isShareholderDoc) {
                            path = `shareholders.${index}.${documentType.split('.').pop()}`;
                        } else if (isBuyerDoc) {
                            path = `kyc.buyers.${index}.${documentType.split('.').pop()}`; // Assumes nested structure
                        } else {
                            // Use mapping for general docs
                            path = documentMappings[documentType] || documentType; // Fallback to type itself? Risky.
                        }

                        if (!path) {
                            console.error(`Could not determine state path for document type: ${documentType}`);
                            throw new Error("Internal error: Document path mapping failed.");
                        }

                        const keys = path.split('.');
                        // Traverse and create path if necessary
                        for (let i = 0; i < keys.length - 1; i++) {
                            const key = keys[i];
                            const nextKeyIsIndex = /^\d+$/.test(keys[i + 1]);
                            if (current[key] === undefined || current[key] === null) {
                                current[key] = nextKeyIsIndex ? [] : {};
                            }
                            current = current[key];
                            // Handle array index traversal
                            if (nextKeyIsIndex) {
                                const arrIndex = parseInt(keys[i + 1], 10);
                                // Ensure array exists and item exists (or create if necessary - carefully)
                                if (!Array.isArray(current)) current = []; // Force array if not one
                                while (current.length <= arrIndex) current.push({}); // Add empty objects up to index
                                current = current[arrIndex]; // Move into the array item
                                i++; // Skip the index key in the next iteration
                            }
                        }
                        // Set the document data at the final key
                        const finalKey = keys[keys.length - 1];
                        if (current && typeof current === 'object') {
                            current[finalKey] = newDocData;
                            console.log(`Updated document in state at path: ${path}`);
                        } else {
                            console.error(`Cannot set document data. Invalid path or target is not an object at key ${finalKey}. Path: ${path}`);
                            throw new Error("Internal error: Failed to set document data in state.");
                        }

                        finalUpdatedState = newData; // Mark state update as successful
                        return newData; // Return the successfully updated state
                    } catch (e) {
                        console.error("Error placing uploaded doc data into state:", e);
                        setSaveError("Error updating UI after upload. Please save and reload.");
                        finalUpdatedState = null; // Mark state update as failed
                        return prevData; // Return previous state on error
                    }
                });

                // If formData update was successful, also update userData for consistency
                if (finalUpdatedState) {
                    const clonedFinalState = deepClone(finalUpdatedState);
                    if (clonedFinalState) {
                        setUserData(clonedFinalState); // Sync userData immediately
                        console.log("Updated userData state after successful document upload.");
                    } else {
                        console.warn("Failed to clone final state for userData update after upload.");
                        // Consider fetching data again as a recovery mechanism if cloning fails
                    }
                } else {
                    console.warn("formData update failed after upload, userData not synced.");
                }

            } else {
                // Throw error if response indicates failure or lacks expected data
                throw new Error(response?.error || response?.message || "Upload failed or returned invalid data.");
            }
        } catch (error) {
            console.error(`Error uploading document (${documentType}, index ${index}):`, error);
            setSaveError(`Error uploading ${label}: ${error.message}`);
            setUploadingDoc(prev => ({ ...prev, [uploadKey]: 'error' })); // Mark specific upload as failed
        } finally {
            // Ensure loading state is turned off, regardless of success/failure
            setUploadingDoc(prev => ({ ...prev, [uploadKey]: false }));
            // Clear the file input value so the same file can be re-selected if needed
            if (fileInputRefs.current[uploadKey]) {
                fileInputRefs.current[uploadKey].value = null;
            }
        }
    };

    // --- Trigger hidden file input ---
    const triggerFileInput = (key) => {
        if (fileInputRefs.current[key]) {
            fileInputRefs.current[key].click();
        } else {
            console.warn(`File input ref not found for key: ${key}`);
        }
    };

    // --- Save Changes Handler ---
    const handleSaveChanges = async () => {
        if (!formData || !userData) {
            setSaveError("Data not loaded correctly. Cannot save.");
            return;
        }
        const userId = userData._id || userData.id;
        if (!userId) {
            setSaveError("User ID is missing. Cannot save.");
            return;
        }

        setIsSaving(true);
        setSaveError('');
        let kycUpdateSuccess = false;
        let shareholderUpdateSuccess = false;
        let buyerUpdateSuccess = false;
        let combinedResponseData = null; // To store merged results from successful updates

        try {
            // --- 1. Prepare and Send KYC Update Payload (Main Details, Directors etc.) ---
            // Send only fields relevant to the updateKyc endpoint
            const kycPayload = {
                id: userId,
                // Root fields if editable in profile
                firstName: formData.firstName,
                lastName: formData.lastName,
                middleName: formData.middleName,
                mobileNo: formData.mobileNo,
                email: formData.email, // Usually email is not updatable via profile, but include if it is
                licenseNumber: formData.licenseNumber,
                // KYC object
                kyc: {
                    ...(formData.kyc || {}), // Spread existing kyc fields first
                    // Explicitly include nested objects if they are managed by updateKyc
                    businessDetails: formData.kyc?.businessDetails || {},
                    employmentDetails: formData.kyc?.employmentDetails || {},
                    incomeDetails: formData.kyc?.incomeDetails || {},
                    address: formData.kyc?.address || {}, // User's primary address
                    directors: formData.kyc?.directors || [], // Send directors if updateKyc handles them
                    // *** EXCLUDE BUYERS and SHAREHOLDERS from this payload ***
                    buyers: formData.kyc?.buyers,
                    // We also need to unset document fields if they aren't sent
                    qatariId: undefined,
                    passport: undefined,
                    utilityBill: undefined,
                    // Ensure nested doc fields are also unset
                    // employmentDetails: { ...formData.kyc?.employmentDetails, employmentLetter: undefined },
                    // incomeDetails: { ...formData.kyc?.incomeDetails, proofOfIncome: undefined }
                },
                // *** EXCLUDE SHAREHOLDERS from root payload ***
                shareholders: undefined,
                // Exclude root document fields
                commercialRegistration: undefined, tradeLicense: undefined, taxCard: undefined,
                establishmentCard: undefined, bankStatement: undefined, auditedFinancialReport: undefined,
                cashFlowLedger: undefined, commercialCreditReport: undefined, memorandumOfAssociation: undefined,
                articleOfAssociation: undefined,
                otherDocument: undefined,
                otherDocumentTwo: undefined,
                otherDocument3: undefined,
                otherDocument4: undefined,
                otherDocument5: undefined,
                otherDocument6: undefined,
                otherDocument7: undefined,
                otherDocument8: undefined,
                otherDocument9: undefined,
                otherDocument10: undefined,
                invoiceAdditionalDoc1: undefined, invoiceAdditionalDoc2: undefined
            };

            // Deep clean undefined values from the payload
            const cleanPayload = (obj) => {
                if (!obj) return obj;
                Object.keys(obj).forEach(key => {
                    if (obj[key] === undefined) {
                        delete obj[key];
                    } else if (typeof obj[key] === 'object' && obj[key] !== null) {
                        // Recurse only if it's a plain object (not array, date, etc.)
                        if (Object.prototype.toString.call(obj[key]) === '[object Object]') {
                            cleanPayload(obj[key]);
                            // Delete empty objects after cleaning
                            if (Object.keys(obj[key]).length === 0) {
                                delete obj[key];
                            }
                        }
                    }
                });
                return obj;
            };
            const cleanedKycPayload = cleanPayload(deepClone(kycPayload)); // Clean a clone


            console.log("Saving Profile (KYC Part) changes with payload:", JSON.stringify(cleanedKycPayload, null, 2));
            const kycResponse = await updateKyc(cleanedKycPayload);

            if (!kycResponse?.success) {
                throw new Error(`Failed to save main profile details: ${kycResponse?.message || 'Unknown error'}`);
            }
            console.log("Main Profile Update successful:", kycResponse);
            kycUpdateSuccess = true;
            // Store the returned user data as the base for further merging
            if (kycResponse.user) combinedResponseData = deepClone(kycResponse.user);


            // --- 2. Prepare and Send Shareholder Text Update ---
            const shareholdersToUpdate = formData.shareholders || [];
            if (shareholdersToUpdate.length > 0) {
                const shareholderPayload = {
                    userId: userId,
                    // Send only fields relevant for text update
                    shareholders: shareholdersToUpdate.map(sh => ({
                        _id: sh._id, // Essential for matching existing shareholders
                        firstName: sh.firstName,
                        middleName: sh.middleName,
                        lastName: sh.lastName,
                        email: sh.email,
                        address: sh.address || {},
                        // DO NOT send document objects (passport, qid, proofOfAddress) here
                    }))
                };
                console.log("Saving Shareholder text changes with payload:", JSON.stringify(shareholderPayload, null, 2));
                const shareholderResponse = await updateShareholderTextData(shareholderPayload);

                if (!shareholderResponse?.success) {
                    setSaveError(`Main details saved, but failed to save shareholder details: ${shareholderResponse?.message || 'Unknown error'}`);
                    shareholderUpdateSuccess = false; // Mark as failed but continue
                } else {
                    console.log("Shareholder Text Update successful:", shareholderResponse);
                    shareholderUpdateSuccess = true;
                    // Merge updated shareholder data into our combined response object
                    if (shareholderResponse.shareholders && combinedResponseData) {
                        combinedResponseData.shareholders = deepClone(shareholderResponse.shareholders);
                    }
                }
            } else {
                shareholderUpdateSuccess = true; // No shareholders, so trivially successful
            }

            // --- 3. Prepare and Send Buyer Update ---
            // const buyersToUpdate = formData.kyc?.buyers || [];
            // if (buyersToUpdate.length > 0) {
            //     const buyerPayload = {
            //         userId: userId,
            //         // Send relevant buyer fields, excluding documents
            //         buyers: buyersToUpdate.map(b => ({
            //             _id: b._id, // Essential for matching existing buyers
            //             buyerName: b.buyerName,
            //             buyerAddress: b.buyerAddress,
            //             contactPerson: b.contactPerson,
            //             contactPhone: b.contactPhone,
            //             contactEmail: b.contactEmail,
            //             businessType: b.businessType,
            //             registrationNumber: b.registrationNumber,
            //             annualPurchaseVolume: b.annualPurchaseVolume,
            //             paymentTerms: b.paymentTerms,
            //             // companyDocument: undefined // Ensure document is not sent
            //         }))
            //     };
            //     console.log("Saving Buyer changes with payload:", JSON.stringify(buyerPayload, null, 2));
            //     const buyerResponse = await updateBuyers(buyerPayload);

            //     if (!buyerResponse?.success) {
            //         setSaveError(prevError => `${prevError ? prevError + '; ' : ''}Failed to save buyer details: ${buyerResponse?.message || 'Unknown error'}`);
            //         buyerUpdateSuccess = false; // Mark as failed
            //     } else {
            //         console.log("Buyer Update successful:", buyerResponse);
            //         buyerUpdateSuccess = true;
            //         // Merge updated buyer data into our combined response object
            //         if (buyerResponse.buyers && combinedResponseData) {
            //             if (!combinedResponseData.kyc) combinedResponseData.kyc = {}; // Ensure kyc object exists
            //             combinedResponseData.kyc.buyers = deepClone(buyerResponse.buyers); // Update buyers array
            //         }
            //     }
            // } else {
            //     buyerUpdateSuccess = true; // No buyers, trivially successful
            // }

            // --- 4. Final Success/Error Handling ---
            if (kycUpdateSuccess && shareholderUpdateSuccess) {
                // All parts succeeded. Use the merged data if available, otherwise fall back to current formData.
                // The combinedResponseData should ideally contain the fully updated user state.
                const finalSavedData = combinedResponseData ? deepClone(combinedResponseData) : deepClone(formData); // Prefer response data

                if (finalSavedData) {
                    // IMPORTANT: Apply defaults again to the saved data before setting state
                    // This ensures consistency in structure, especially for docs
                    const finalDataWithDefaults = applyDefaultsAndStructure(finalSavedData); // Need to define this helper or do it inline

                    setUserData(finalDataWithDefaults); // Update the main view data
                    setFormData(deepClone(finalDataWithDefaults)); // Reset form data to the fully updated state
                    setIsEditing(false); // Exit edit mode
                    alert("Profile updated successfully!");
                } else {
                    console.error("Failed to clone final state after successful save.");
                    // Fallback: Maybe just exit edit mode? Or show error?
                    setIsEditing(false);
                    alert("Profile saved, but UI update failed. Please reload.");
                }
            } else {
                // Partial success: Update state with whatever succeeded, report errors.
                console.warn("Partial save failure occurred.");
                if (combinedResponseData) {
                    const partiallySavedData = deepClone(combinedResponseData);
                    if (partiallySavedData) {
                        // Fill in the failed parts from the formData to keep UI consistent with edits
                        if (!shareholderUpdateSuccess) {
                            partiallySavedData.shareholders = formData.shareholders;
                        }
                        if (!buyerUpdateSuccess) {
                            if (!partiallySavedData.kyc) partiallySavedData.kyc = {};
                            partiallySavedData.kyc.buyers = formData.kyc?.buyers;
                        }
                        // Apply defaults/structure to the partially saved data
                        const partialDataWithDefaults = applyDefaultsAndStructure(partiallySavedData);

                        setUserData(partialDataWithDefaults); // Update view with best available data
                        setFormData(deepClone(partialDataWithDefaults)); // Sync form state
                        console.log("Partially updated profile state after save attempt.");
                    } else {
                        console.error("Failed to clone partially saved state.");
                    }
                } else {
                    // If even the initial KYC update failed, combinedResponseData might be null
                    console.error("Initial KYC save likely failed, cannot update state partially.");
                }
                // Do NOT exit edit mode on partial failure. SaveError state should already be set.
                setIsEditing(true);
                alert("Some profile changes could not be saved. Please review errors and try again.");
            }

        } catch (error) {
            // Catch errors from the initial KYC update or unexpected issues
            console.error("Error during profile save process:", error);
            setSaveError(prevError => `${prevError ? prevError + '; ' : ''}Save failed: ${error.message}`);
            // Attempt partial update if KYC part succeeded but an error occurred later
            if (kycUpdateSuccess && combinedResponseData && !(shareholderUpdateSuccess && buyerUpdateSuccess)) {
                // Logic similar to partial success case above
                try {
                    const partiallySavedData = deepClone(combinedResponseData);
                    if (partiallySavedData) {
                        if (!shareholderUpdateSuccess) partiallySavedData.shareholders = formData.shareholders;
                        if (!buyerUpdateSuccess) {
                            if (!partiallySavedData.kyc) partiallySavedData.kyc = {};
                            partiallySavedData.kyc.buyers = formData.kyc?.buyers;
                        }
                        const partialDataWithDefaults = applyDefaultsAndStructure(partiallySavedData);
                        setUserData(partialDataWithDefaults);
                        setFormData(deepClone(partialDataWithDefaults));
                        console.log("Partially updated profile state after catching error.");
                    }
                } catch (cloneError) {
                    console.error("Failed to clone state during error handling:", cloneError);
                }
            }
            setIsEditing(true); // Stay in edit mode on error
        } finally {
            setIsSaving(false); // Ensure saving indicator stops
        }
    };

    // Helper function to apply defaults and structure (needed after save)
    const applyDefaultsAndStructure = (data) => {
        if (!data) return null;
        let structuredData = deepClone(data); // Work on a clone
        // Apply all the same default initializations as in fetchData
        structuredData.kyc = structuredData.kyc || {};
        structuredData.kyc.businessDetails = structuredData.kyc.businessDetails || {};
        structuredData.kyc.employmentDetails = structuredData.kyc.employmentDetails || {};
        structuredData.kyc.incomeDetails = structuredData.kyc.incomeDetails || {};
        structuredData.shareholders = Array.isArray(structuredData.shareholders) ? structuredData.shareholders : [];
        structuredData.kyc.buyers = Array.isArray(structuredData.kyc.buyers) ? structuredData.kyc.buyers : [];
        structuredData.kyc.directors = Array.isArray(structuredData.kyc.directors) ? structuredData.kyc.directors : [];
        structuredData.kyc.address = structuredData.kyc.address || {};
        structuredData.shareholders.forEach(sh => { sh.address = sh.address || {}; });
        // Apply ensureDocStructure to all document fields...
        structuredData.commercialRegistration = ensureDocStructure(structuredData.commercialRegistration);
        structuredData.tradeLicense = ensureDocStructure(structuredData.tradeLicense);
        structuredData.taxCard = ensureDocStructure(structuredData.taxCard);
        structuredData.establishmentCard = ensureDocStructure(structuredData.establishmentCard);
        structuredData.bankStatement = ensureDocStructure(structuredData.bankStatement);
        structuredData.auditedFinancialReport = ensureDocStructure(structuredData.auditedFinancialReport);
        structuredData.cashFlowLedger = ensureDocStructure(structuredData.cashFlowLedger);
        structuredData.commercialCreditReport = ensureDocStructure(structuredData.commercialCreditReport);
        structuredData.memorandumOfAssociation = ensureDocStructure(structuredData.memorandumOfAssociation);
        structuredData.articleOfAssociation = ensureDocStructure(structuredData.articleOfAssociation);
        structuredData.otherDocument = ensureDocStructure(structuredData.otherDocument);
        structuredData.otherDocumentTwo = ensureDocStructure(structuredData.otherDocumentTwo);
        structuredData.otherDocument3 = ensureDocStructure(structuredData.otherDocument3);
        structuredData.otherDocument4 = ensureDocStructure(structuredData.otherDocument4);
        structuredData.otherDocument5 = ensureDocStructure(structuredData.otherDocument5);
        structuredData.otherDocument6 = ensureDocStructure(structuredData.otherDocument6);
        structuredData.otherDocument7 = ensureDocStructure(structuredData.otherDocument7);
        structuredData.otherDocument8 = ensureDocStructure(structuredData.otherDocument8);
        structuredData.otherDocument9 = ensureDocStructure(structuredData.otherDocument9);
        structuredData.otherDocument10 = ensureDocStructure(structuredData.otherDocument10);
        structuredData.kyc.qatariId = ensureDocStructure(structuredData.kyc.qatariId);
        structuredData.kyc.passport = ensureDocStructure(structuredData.kyc.passport);
        structuredData.kyc.utilityBill = ensureDocStructure(structuredData.kyc.utilityBill);
        structuredData.kyc.incomeDetails.proofOfIncome = ensureDocStructure(structuredData.kyc.incomeDetails.proofOfIncome);
        structuredData.kyc.employmentDetails.employmentLetter = ensureDocStructure(structuredData.kyc.employmentDetails.employmentLetter);
        structuredData.shareholders.forEach(sh => {
            sh.passport = ensureDocStructure(sh.passport);
            sh.qid = ensureDocStructure(sh.qid);
            sh.proofOfAddress = ensureDocStructure(sh.proofOfAddress);
        });
        // Buyer companyDocument structure handled in buyer array mapping above
        return structuredData;
    };


    // --- Render Helper: Detail Item (Combined View/Edit Logic - Preferred for Profile) ---
    const renderEditableDetailItem = (label, viewValue, editPath, inputType = 'text', options = null) => {
        // Use viewValue for display, calculate editValue from formData
        const displayValue = (viewValue !== null && viewValue !== undefined && String(viewValue).trim() !== '')
            ? (inputType === 'date' ? formatDate(viewValue) : (typeof viewValue === 'boolean' ? (viewValue ? 'Yes' : 'No') : String(viewValue)))
            : <span className="text-gray-400 italic">Not Provided</span>;

        const editValue = getNestedValue(formData, editPath, '');

        const inputProps = {
            id: editPath,
            name: editPath, // Use path as name
            // Format value for specific input types
            value: inputType === 'date' ? formatDateForInput(editValue) : editValue,
            onChange: (e) => {
                const targetValue = inputType === 'checkbox' ? e.target.checked : (inputType === 'booleanSelect' ? (e.target.value === 'true' ? true : (e.target.value === 'false' ? false : null)) : e.target.value);
                handleInputChange(editPath, targetValue);
            },
            className: "block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm py-1.5 px-2",
        };
        const checkboxClass = "h-4 w-4 text-indigo-600 border-gray-300 rounded focus:ring-indigo-500";

        return (
            <div className="py-2 px-1 sm:grid sm:grid-cols-3 sm:gap-4 border-b border-gray-100 last:border-b-0 items-center">
                <dt className="text-sm font-medium text-gray-500 whitespace-normal break-words">{label}</dt>
                <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2 break-words">
                    {isEditing ? (
                        inputType === 'select' && options ? (
                            <select {...inputProps}>
                                <option value="">Select...</option>
                                {options.map(opt => <option key={opt.value} value={opt.value}>{opt.label}</option>)}
                            </select>
                        ) : inputType === 'date' ? (
                            <input type="date" {...inputProps} />
                        ) : inputType === 'checkbox' ? (
                            <input type="checkbox" {...inputProps} checked={!!editValue} /> // Ensure boolean check
                        ) : inputType === 'booleanSelect' ? (
                            <select {...inputProps} value={editValue === true ? 'true' : editValue === false ? 'false' : ''}>
                                <option value="">Select...</option>
                                <option value="true">Yes</option>
                                <option value="false">No</option>
                            </select>
                        ) : inputType === 'number' ? (
                            <input type="number" {...inputProps} />
                        ) : ( // Default to text
                            <input type={inputType} {...inputProps} />
                        )
                    ) : (
                        displayValue // Display formatted value in view mode
                    )}
                </dd>
            </div>
        );
    };

    // --- Render Helper: Address (Combined View/Edit) ---
    const renderEditableAddress = (
        _addressObjForView, // First argument might be less relevant now for view mode
        basePathForEdit,    // e.g., 'kyc' or 'kyc.businessDetails'
        title = "Address",
        isBuyer = false
    ) => {
        let entityType = 'user';
        if (basePathForEdit.includes('businessDetails')) entityType = 'business';
        else if (basePathForEdit.startsWith('shareholders.')) entityType = 'shareholder';
        else if (isBuyer || basePathForEdit.startsWith('kyc.buyers.')) entityType = 'buyer'; // Adjusted buyer detection

        // Define fields with correct keys for each type
        const userAddressFields = [
            { label: "Address Line 1", key: "addressLine1", path: `${basePathForEdit}.addressLine1` },
            { label: "Address Line 2", key: "addressLine2", path: `${basePathForEdit}.addressLine2` },
            { label: "City", key: "city", path: `${basePathForEdit}.city` },
            { label: "Country", key: "country", path: `${basePathForEdit}.country` },
            // { label: "State", key: "state", path: `${basePathForEdit}.state` },
            // { label: "Postal Code", key: "postalCode", path: `${basePathForEdit}.postalCode` }
        ];
        const businessAddressFields = [
            { label: "Address Line 1", key: "businessAddressLine1", path: `${basePathForEdit}.businessAddressLine1` },
            { label: "Address Line 2", key: "businessAddressLine2", path: `${basePathForEdit}.businessAddressLine2` },
            { label: "City", key: "businessCity", path: `${basePathForEdit}.businessCity` },
            { label: "Country", key: "businessCountry", path: `${basePathForEdit}.businessCountry` }
            // Add Zone, Street No, Building No for business if needed
        ];
        const shareholderAddressFields = [
            { label: "Zone", key: "zone", path: `${basePathForEdit}.zone` },
            { label: "Street No.", key: "streetNo", path: `${basePathForEdit}.streetNo` },
            { label: "Building No.", key: "buildingNo", path: `${basePathForEdit}.buildingNo` },
            { label: "Floor No.", key: "floorNo", path: `${basePathForEdit}.floorNo` },
            { label: "Unit No. / Flat No.", key: "unitNo", path: `${basePathForEdit}.unitNo` },
            // { label: "Additional Landmark", key: "additionalLandmark", path: `${basePathForEdit}.additionalLandmark` }
        ];
        const buyerAddressFields = [ // Assuming buyer address structure is simple
            { label: "Buyer Address", key: "buyerAddress", path: `${basePathForEdit}.buyerAddress` }
        ];

        // Select the correct fields based on entity type
        let fields = entityType === 'business' ? businessAddressFields
            : entityType === 'shareholder' ? shareholderAddressFields
                : entityType === 'buyer' ? buyerAddressFields
                    : userAddressFields; // Default to user address fields

        // Determine array context for handleArrayItemChange (remains the same)
        let arrayName = null;
        let itemIndex = null;
        if (basePathForEdit.startsWith('shareholders.')) {
            const match = basePathForEdit.match(/shareholders\.(\d+)/);
            if (match) { arrayName = 'shareholders'; itemIndex = parseInt(match[1], 10); }
        } else if (basePathForEdit.startsWith('kyc.buyers.')) {
            const match = basePathForEdit.match(/kyc\.buyers\.(\d+)/);
            if (match) { arrayName = 'kyc.buyers'; itemIndex = parseInt(match[1], 10); }
        }

        // *** MODIFIED: Get the object to VIEW data from based on formData and basePath ***
        // Assumes 'formData' state variable holds the full user data structure
        const viewObject = getNestedValue(formData, basePathForEdit, {}); // e.g., gets formData.kyc or formData.kyc.businessDetails

        // Check if the derived view object has any displayable data using the CORRECT keys
        const hasOriginalData = viewObject && typeof viewObject === 'object' && fields.some(f => {
            // Use the 'key' specific to the address type (e.g., 'addressLine1' or 'businessAddressLine1')
            const valueFromViewObject = getNestedValue(viewObject, f.key, null);
            return valueFromViewObject !== null && valueFromViewObject !== undefined && String(valueFromViewObject).trim() !== '';
        });

        return (
            <div className="py-2 px-0">
                <dt className="text-sm font-medium text-gray-500 mb-1">{title}</dt>
                <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2 pl-4 border-l-2 border-gray-200 ml-1 space-y-1">
                    {/* Use the derived viewObject for display logic */}
                    {(isEditing || hasOriginalData) ? (
                        fields.map(field => {
                            // *** MODIFIED: Get original value from the derived viewObject using the specific field key ***
                            const originalValue = getNestedValue(viewObject, field.key, '');
                            // Edit value still uses the full path relative to the main formData
                            const editValue = getNestedValue(formData, field.path, '');

                            // Skip rendering empty fields in view mode (unless editing)
                            if (!isEditing && (originalValue === null || originalValue === undefined || String(originalValue).trim() === '')) {
                                return null;
                            }

                            return (
                                <div key={field.path} className="py-1 px-1 sm:grid sm:grid-cols-3 sm:gap-2 border-b border-gray-100 last:border-b-0 items-center">
                                    <dt className="text-xs font-medium text-gray-500">{field.label}</dt>
                                    <dd className="mt-1 text-xs text-gray-900 sm:mt-0 sm:col-span-2">
                                        {isEditing ? (
                                            <input
                                                type="text"
                                                id={field.path}
                                                name={field.path}
                                                value={editValue} // Bind to the value derived from formData via full path
                                                onChange={(e) => {
                                                    if (arrayName !== null && itemIndex !== null) {
                                                        // Calculate relative path from item base path
                                                        // Example: field.path = shareholders.0.address.zone, basePathForEdit = shareholders.0.address
                                                        // Need relative path 'zone'
                                                        const relativeFieldPath = field.path.substring(basePathForEdit.length + 1);
                                                        handleArrayItemChange(arrayName, itemIndex, relativeFieldPath, e.target.value); // This needs adjustment if basePath isn't item path
                                                        // TODO: Verify handleArrayItemChange logic based on actual array structure and paths
                                                    } else {
                                                        handleInputChange(field.path, e.target.value); // For non-array addresses like kyc or kyc.businessDetails
                                                    }
                                                }}
                                                className="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-xs py-1 px-1.5" />
                                        ) : (
                                            // Display the originalValue derived correctly from viewObject
                                            (originalValue !== null && originalValue !== undefined && String(originalValue).trim() !== '')
                                                ? String(originalValue)
                                                : <span className="text-gray-400 italic text-xs">Not Provided</span>
                                        )}
                                    </dd>
                                </div>);
                        })
                    ) : (
                        <div className="py-1 px-0 text-xs text-gray-400 italic">Not Provided</div>
                    )}
                </dd>
            </div>);
    };

    // --- Render Helper: Document (Combined View/Edit) ---
    const renderDocument = (docDataForView, label, documentType, index = null) => {
        // Use docDataForView (from userData) for displaying current state
        const currentDoc = ensureDocStructure(docDataForView);
        const uploadKey = index !== null ? `${documentType}-${index}` : documentType; // Unique key for tracking
        const isUploading = uploadingDoc[uploadKey] === true; // Check for true specifically
        const uploadFailed = uploadingDoc[uploadKey] === 'error';
        const hasDoc = !!currentDoc.filePath;
        const fullFileName = hasDoc ? getFileNameFromPath(currentDoc.filePath) : 'Not Uploaded';
        const fileName = hasDoc ? shortenDocumentName(fullFileName) : 'Not Uploaded';
        const viewUrl = currentDoc.signedUrl;

        // Determine if upload is possible for this type
        const isShareholderOrBuyerDoc = documentType.startsWith('shareholder') || documentType.startsWith('buyer');
        const canUpload = isEditing && !isUploading && (documentMappings[documentType] || isShareholderOrBuyerDoc); // Enable for known types/arrays when editing

        // Special handling for buyer documents (currently read-only)
        const isBuyerUploadDisabled = isEditing && documentType.startsWith('buyer'); // Disable buyer uploads for now

        return (
            <div className={`py-3 px-4 flex flex-col sm:flex-row justify-between items-start sm:items-center text-sm border rounded-md ${hasDoc ? 'border-gray-200 bg-white' : 'border-dashed border-gray-300 bg-gray-50'} ${uploadFailed ? 'border-red-300 bg-red-50' : ''}`}>
                {/* Hidden File Input - Conditionally render only if upload is possible */}
                {canUpload && !isBuyerUploadDisabled && (
                    <input type="file" accept=".pdf,.jpg,.jpeg,.png" ref={ref => fileInputRefs.current[uploadKey] = ref}
                        onChange={(e) => handleFileReplace(documentType, e.target.files?.[0], index, label)}
                        className="hidden" disabled={!canUpload || isUploading || isBuyerUploadDisabled} />
                )}

                {/* Label */}
                <span className={`font-medium mb-1 sm:mb-0 ${uploadFailed ? 'text-red-700' : 'text-gray-700'}`}>{label}</span>

                {/* Actions & Status */}
                <div className="flex items-center space-x-2 text-right w-full sm:w-auto justify-end flex-wrap min-w-0">
                    {isUploading ? (
                        <> <ArrowPathIcon className="w-4 h-4 text-blue-500 animate-spin flex-shrink-0 order-1" /> <span className="text-xs text-blue-600 order-2">Uploading...</span> </>
                    ) : uploadFailed ? (
                        <> <ExclamationTriangleIcon className="w-5 h-5 text-red-500 flex-shrink-0 order-1" /> <span className="text-xs text-red-600 order-2">Upload Failed</span>
                            {canUpload && !isBuyerUploadDisabled && (<button type="button" onClick={() => triggerFileInput(uploadKey)} className="text-xs text-gray-500 hover:underline p-1 order-4" title="Retry Upload"><ArrowPathIcon className="w-4 h-4 inline mr-0.5" /> Retry </button>)}
                        </>
                    ) : hasDoc ? (
                        <>
                            <CheckCircleIcon className="w-5 h-5 text-green-500 flex-shrink-0 order-1" aria-hidden="true" />
                            <span className="text-gray-800 truncate flex-shrink min-w-0 order-2 text-xs" title={fullFileName}>{fileName}</span>
                            {viewUrl && (<button type="button" onClick={() => openDocumentViewer(viewUrl)} className="text-xs text-indigo-600 hover:underline p-1 order-3" title="View Document"><EyeIcon className="w-4 h-4 inline mr-0.5" /> View</button>)}
                            {canUpload && !isBuyerUploadDisabled && (<button type="button" onClick={() => triggerFileInput(uploadKey)} className="text-xs text-gray-500 hover:underline p-1 order-4" title="Replace Document"><ArrowPathIcon className="w-4 h-4 inline mr-0.5" /> Replace</button>)}
                        </>
                    ) : ( // No document uploaded yet
                        isEditing ? (
                            isBuyerUploadDisabled ? (
                                <span className="text-gray-400 italic text-sm order-1 flex items-center"><NoSymbolIcon className="w-4 h-4 mr-1 text-gray-300" /> Upload Not Available</span>
                            ) : canUpload ? (
                                <button type="button" onClick={() => triggerFileInput(uploadKey)} className="text-xs text-indigo-600 hover:underline p-1 order-1" title="Upload Document"><ArrowUpTrayIcon className="w-4 h-4 inline mr-0.5" /> Upload</button>
                            ) : ( // Cannot upload (e.g., unsupported type)
                                <span className="text-gray-400 italic text-sm order-1 flex items-center"><NoSymbolIcon className="w-4 h-4 mr-1 text-gray-300" /> Not Uploaded</span>
                            )
                        ) : ( // View mode, no doc
                            <span className="text-gray-400 italic text-sm order-1 flex items-center"><NoSymbolIcon className="w-4 h-4 mr-1 text-gray-300" /> Not Uploaded</span>
                        )
                    )}
                </div>
            </div>);
    };

    // --- Render Helper: Single Buyer Item (Combined View/Edit) ---
    const renderEditableBuyerItem = (label, viewValue, arrayPath, index, fieldPath, inputType = 'text') => {
        // Use viewValue for display, calculate editValue from formData
        const displayValue = (viewValue !== null && viewValue !== undefined && String(viewValue).trim() !== '')
            ? String(viewValue) // Display raw value, maybe format later if needed
            : <span className="text-gray-400 italic">Not Provided</span>;

        const editValue = getNestedValue(formData, `${arrayPath}.${index}.${fieldPath}`, '');

        const inputProps = {
            id: `${arrayPath}-${index}-${fieldPath}`, // Unique ID
            name: `${arrayPath}-${index}-${fieldPath}`,
            value: editValue, // Use raw value for input
            onChange: (e) => {
                // Determine correct value type (e.g., for numbers)
                let targetValue = e.target.value;
                if (inputType === 'number') {
                    targetValue = targetValue === '' ? null : Number(targetValue); // Allow empty or convert to number
                }
                handleArrayItemChange(arrayPath, index, fieldPath, targetValue);
            },
            className: "block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm py-1.5 px-2",
        };

        return (
            <div className="py-2 px-1 sm:grid sm:grid-cols-3 sm:gap-4 border-b border-gray-100 last:border-b-0 items-center">
                <dt className="text-sm font-medium text-gray-500 whitespace-normal break-words">{label}</dt>
                <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2 break-words">
                    {isEditing ? (
                        inputType === 'number' ? (
                            <input type="number" {...inputProps} step="any" /> // Allow decimals if needed
                        ) : ( // Default to text
                            <input type={inputType} {...inputProps} />
                        )
                    ) : (
                        displayValue // Display value in view mode
                    )}
                </dd>
            </div>
        );
    };


    // --- Loading/Error States ---
    if (isLoading) {
        return <LoadingModal />
    }

    if (loadError) {
        return (
            <div className="container mx-auto p-6 max-w-4xl">
                <div className="p-6 bg-red-50 border border-red-300 text-red-800 rounded-lg shadow-md">
                    <h2 className="text-xl font-semibold mb-2">Error Loading Profile</h2>
                    <p>{loadError}</p>
                    <button onClick={() => window.location.reload()} className="mt-3 px-3 py-1 bg-red-600 text-white text-sm rounded hover:bg-red-700">Reload</button>
                </div>
            </div>
        );
    }

    // Ensure data is loaded before rendering main content
    if (!userData || !formData) {
        return <div className="text-center p-10 text-gray-500">Profile data could not be fully loaded. Please try reloading.</div>;
    }

    // Safe access for potentially missing nested data in view mode
    const viewKyc = userData || {};

    console.log(viewKyc, "VIEWKYC HERE")
    const viewBusinessDetails = viewKyc.businessDetails || {};
    const viewShareholders = userData.shareholders || [];
    const viewBuyers = viewKyc.buyers || [];
    const viewDirectors = viewKyc.directors || [];
    const viewUserAddress = viewKyc.address || {};
    const viewEmploymentDetails = viewKyc.employmentDetails || {};
    const viewIncomeDetails = viewKyc.incomeDetails || {};

    // Form data access (should generally exist if userData exists due to cloning)
    const formKyc = formData.kyc || {};
    const formShareholders = formData.shareholders || [];
    const formBuyers = formKyc.buyers || [];

    // --- Component Render ---
    return (
        <div className="bg-gray-50 min-h-screen py-8 px-4 sm:px-6 lg:px-8">
            <div className="max-w-4xl mx-auto bg-white shadow-xl rounded-lg overflow-hidden">
                {/* Header */}
                <div className="px-6 py-5 bg-gray-100 border-b border-gray-200 flex justify-between items-center flex-wrap gap-4">
                    <h2 className="text-xl font-semibold text-gray-800">Your Profile</h2>
                    {(kycStatus === 'INITIATED' || kycStatus === 'UNDER_REVIEW') && (
                        <button
                            type="button"
                            onClick={handleEditToggle}
                            className={`inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2 transition-colors duration-150 ease-in-out ${isEditing
                                ? 'bg-red-100 text-red-700 hover:bg-red-200 focus:ring-red-500'
                                : 'bg-indigo-100 text-indigo-700 hover:bg-indigo-200 focus:ring-indigo-500'
                                }`}
                            disabled={isSaving} // Disable while saving
                        >
                            {isEditing ? (
                                <> <XMarkIcon className="h-4 w-4 mr-1.5" /> Cancel Edit </>
                            ) : (
                                <> <PencilSquareIcon className="h-4 w-4 mr-1.5" /> Edit Profile </>
                            )}
                        </button>
                    )}

                </div>

                {/* Save Error Display */}
                {saveError && (
                    <div className="m-4 p-4 bg-red-50 border border-red-300 rounded-md text-sm text-red-700">
                        <strong>Save Error:</strong> {saveError}
                    </div>
                )}

                {/* Edit Mode Notice */}
                {isEditing && (
                    <div className="m-4 p-3 bg-yellow-50 border border-yellow-300 rounded-md text-sm text-yellow-800 flex items-start gap-2">
                        <InformationCircleIcon className="h-5 w-5 text-yellow-600 flex-shrink-0" />
                        <span>You are in edit mode. Modify fields and upload documents, then click 'Save Changes'. Document uploads are saved immediately.</span>
                    </div>
                )}


                {/* Form Sections */}
                {/* Wrap sections in a form only when editing */}
                <form onSubmit={(e) => { e.preventDefault(); handleSaveChanges(); }}>
                    <div className="p-6 space-y-8">

                        {/* 1. Business Details */}
                        <section>
                            <h3 className="text-lg font-semibold text-[#004141] mb-4 pb-2 border-b border-gray-200 flex items-center">
                                <BuildingOffice2Icon className="h-6 w-6 mr-2 text-gray-600" /> Business Details
                            </h3>
                            <div className={`p-4 rounded-md border ${isEditing ? 'bg-indigo-50 border-indigo-200' : 'bg-gray-50 border-gray-200'}`}>
                                {/* Use dl with divide-y for overall structure */}
                                <dl className="divide-y divide-gray-100">
                                    {/* --- Fields inside kyc.businessDetails with required fields marked --- */}
                                    {renderEditableDetailItem("Trade Name / Legal Entity Name", getNestedValue(userData, "kyc.businessDetails.businessName"), "kyc.businessDetails.businessName", "text", null, true)}
                                    {renderEditableDetailItem("CR Number", getNestedValue(userData, "kyc.businessDetails.crNumber"), "kyc.businessDetails.crNumber", "text", null, true)}
                                    {renderEditableDetailItem("Legal Form", getNestedValue(userData, "kyc.businessDetails.legalForm"), "kyc.businessDetails.legalForm")}
                                    {renderEditableDetailItem("CR Issue Date", getNestedValue(userData, "kyc.businessDetails.crIssueDate"), "kyc.businessDetails.crIssueDate", "date", null, true)}
                                    {renderEditableDetailItem("CR Expiry Date", getNestedValue(userData, "kyc.businessDetails.crExpiryDate"), "kyc.businessDetails.crExpiryDate", "date", null, true)}
                                    {renderEditableDetailItem("Tax Reg. No.", getNestedValue(userData, "kyc.businessDetails.taxRegNo"), "kyc.businessDetails.taxRegNo", "text", null, true)}
                                    {/* {renderEditableDetailItem("TIN No.", getNestedValue(userData, "kyc.businessDetails.taxRegNo"), "kyc.businessDetails.taxRegNo")} */}
                                    {renderEditableDetailItem("No. of Branches", getNestedValue(userData, "kyc.businessDetails.branchCount"), "kyc.businessDetails.branchCount", "number")}
                                    {renderEditableDetailItem("Firm Nationality", getNestedValue(userData, "kyc.businessDetails.firmNationality"), "kyc.businessDetails.firmNationality")}
                                    {renderEditableDetailItem("Establishment ID", getNestedValue(userData, "kyc.businessDetails.establishmentId"), "kyc.businessDetails.establishmentId")}
                                    {renderEditableDetailItem("Establishment ID Issue Date", getNestedValue(userData, "kyc.businessDetails.establishmentIdIssueDate"), "kyc.businessDetails.establishmentIdIssueDate", "date")}
                                    {renderEditableDetailItem("Establishment ID Expiry Date", getNestedValue(userData, "kyc.businessDetails.establishmentIdExpiryDate"), "kyc.businessDetails.establishmentIdExpiryDate", "date")}
                                    {renderEditableDetailItem("TL Issue Date", getNestedValue(userData, "kyc.businessDetails.tlIssueDate"), "kyc.businessDetails.tlIssueDate", "date")}
                                    {renderEditableDetailItem("TL Expiry Date", getNestedValue(userData, "kyc.businessDetails.tlExpiryDate"), "kyc.businessDetails.tlExpiryDate", "date")}
                                    {renderEditableDetailItem("Ownership Type", getNestedValue(userData, "kyc.businessDetails.ownershipType"), "kyc.businessDetails.ownershipType")}

                                    {/* --- Business Address --- */}
                                    {/* This helper renders the address block */}
                                    {renderEditableAddress(getNestedValue(userData, "kyc.businessDetails"), "kyc.businessDetails", "Business Address", false, true)}

                                    {/* --- Business Address Verification Status (Non-Editable Badge) - Using GRID LAYOUT --- */}
                                    {/* {getNestedValue(userData, "kyc.businessDetails.businessAddressVerification.status") && ( // Check if status exists
                                        <div className="py-2 px-1 sm:grid sm:grid-cols-3 sm:gap-4 items-center">
                                            <dt className="text-sm font-medium text-gray-500">
                                                Address Verification Status
                                            </dt>
                                            <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2"> 
                                                {(() => { 
                                                    const status = getNestedValue(userData, "kyc.businessDetails.businessAddressVerification.status");

                                                    let bgColor = 'bg-gray-100';
                                                    let textColor = 'text-gray-700';
                                                    let IconComponent = InformationCircleIcon; // Default icon
                                                    let text = 'Unknown'; // Default text

                                                    if (status) {
                                                        text = status.charAt(0).toUpperCase() + status.slice(1).toLowerCase().replace(/_/g, ' ');

                                                        switch (status) {
                                                            case 'INITIATED':
                                                                bgColor = 'bg-blue-100'; textColor = 'text-blue-700'; IconComponent = InformationCircleIcon;
                                                                break;
                                                            case 'PENDING':
                                                                bgColor = 'bg-yellow-100'; textColor = 'text-yellow-700'; IconComponent = ClockIcon;
                                                                break;
                                                            case 'VERIFIED':
                                                                bgColor = 'bg-green-100'; textColor = 'text-green-700'; IconComponent = CheckCircleIcon;
                                                                break;
                                                            case 'REJECTED':
                                                                bgColor = 'bg-red-100'; textColor = 'text-red-700'; IconComponent = XCircleIcon;
                                                                break;
                                                            case 'SKIPPED':
                                                                // Keep default gray, maybe specify icon
                                                                IconComponent = InformationCircleIcon;
                                                                break;
                                                            default:
                                                                // Keep default gray for any unexpected statuses
                                                                text = status; // Display raw status if unknown
                                                                break;
                                                        }
                                                    } else {
                                                        text = 'N/A'; // Handle null/undefined status
                                                    }

                                                    return (
                                                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-sm font-medium ${bgColor} ${textColor}`}>
                                                            {IconComponent && <IconComponent className="h-4 w-4 mr-1.5" aria-hidden="true" />}
                                                            {text}
                                                        </span>
                                                    );
                                                })()}
                                            </dd>
                                        </div>
                                    )} */}
                                    {/* --- End Business Address Verification Status --- */}

                                    {/* --- Root Level Fields with required fields marked --- */}
                                    {renderEditableDetailItem("Trade License (TL) Number", getNestedValue(userData, "licenseNumber"), "licenseNumber")}
                                    {renderEditableDetailItem("Business Contact Number", getNestedValue(userData, "mobileNo"), "mobileNo", "text")}
                                    {renderEditableDetailItem("Business Email ID", getNestedValue(userData, "email"), "email", "email", null, true)}

                                    {/* --- Fields inside kyc --- */}
                                    {renderEditableDetailItem("Qatar Based", getNestedValue(userData, "kyc.businessDetails.isQatarBased"), "kyc.isQatarBased", "checkbox")}

                                </dl> {/* End main dl */}
                            </div> {/* End outer div */}
                        </section>

                        {/* 2. Personal Details */}
                        <section>
                            <h3 className="text-lg font-semibold text-[#004141] mb-4 pb-2 border-b border-gray-200 flex items-center">
                                <UserCircleIcon className="h-6 w-6 mr-2 text-gray-600" /> Personal Details
                            </h3>
                            <div className={`p-4 rounded-md border ${isEditing ? 'bg-indigo-50 border-indigo-200' : 'bg-gray-50 border-gray-200'}`}>
                                <dl className="divide-y divide-gray-100">
                                    {renderEditableDetailItem("First Name", getNestedValue(userData, "firstName"), "firstName")}
                                    {renderEditableDetailItem("Middle Name", getNestedValue(userData, "middleName"), "middleName")} {/* Assuming middleName exists at root */}
                                    {renderEditableDetailItem("Last Name", getNestedValue(userData, "lastName"), "lastName")}
                                    {/* {renderEditableDetailItem("D.O.B", getNestedValue(userData, "kyc.dateOfBirth"), "kyc.dateOfBirth", "date")} */}
                                    {/* {renderEditableDetailItem("Gender", getNestedValue(userData, "kyc.gender"), "kyc.gender", "select", [{ label: "Male", value: "M" }, { label: "Female", value: "F" }, { label: "Other", value: "Other" }])} */}
                                    {renderEditableDetailItem("QID Number", getNestedValue(userData, "kyc.qidNumber"), "kyc.qidNumber")}
                                    {renderEditableDetailItem("QID Expiry Date", getNestedValue(userData, "kyc.qidExpiryDate"), "kyc.qidExpiryDate", "date")}
                                    {renderEditableDetailItem("Nationality", getNestedValue(userData, "kyc.nationality"), "kyc.nationality")}
                                    {/* --- Primary Address (User's KYC address) --- */}
                                    {/* Add other fields like maritalStatus, education etc. if needed */}
                                    {/* {renderEditableDetailItem("Marital Status", getNestedValue(userData, "kyc.maritalStatus"), "kyc.maritalStatus")} */}
                                    {/* {renderEditableDetailItem("Education", getNestedValue(userData, "kyc.education"), "kyc.education")} */}
                                    {/* {renderEditableDetailItem("Has WhatsApp?", getNestedValue(userData, "kyc.hasWhatsapp"), "kyc.hasWhatsapp", "booleanSelect")} */}
                                    {/* {renderEditableDetailItem("Home Ownership", getNestedValue(userData, "kyc.homeOwnership"), "kyc.homeOwnership")} */}
                                    {/* {renderEditableDetailItem("Owns Vehicle?", getNestedValue(userData, "kyc.vehicleOwnership"), "kyc.vehicleOwnership", "booleanSelect")} */}
                                    {/* {renderEditableDetailItem("Has Children?", getNestedValue(userData, "kyc.hasChildren"), "kyc.hasChildren", "booleanSelect")} */}

                                </dl>
                            </div>
                        </section>

                        {/* 3. Bank Account Details */}
                        <section>
                            <h3 className="text-lg font-semibold text-[#004141] mb-4 pb-2 border-b border-gray-200 flex items-center">
                                {/* Optional Icon: <BanknotesIcon className="h-6 w-6 mr-2 text-gray-600" /> */}
                                Bank Account Details
                            </h3>
                            <div className={`p-4 rounded-md border ${isEditing ? 'bg-indigo-50 border-indigo-200' : 'bg-gray-50 border-gray-200'}`}>
                                <dl className="divide-y divide-gray-100">
                                    {/* Render Account Number */}
                                    {renderEditableDetailItem(
                                        "Account Number", // Label for the field
                                        getNestedValue(userData, "kyc.incomeDetails.accountNumber"), // Value from original data
                                        "kyc.incomeDetails.accountNumber" // Path for editing formData
                                    )}

                                    {/* Render IFSC Code */}
                                    {renderEditableDetailItem(
                                        "IBAN", // Label for the field
                                        getNestedValue(userData, "kyc.incomeDetails.ifscCode"), // Value from original data
                                        "kyc.incomeDetails.ifscCode" // Path for editing formData
                                    )}
                                </dl>
                            </div>
                        </section>

                        {/* 4. Uploaded Documents */}
                        <section>
                            <h3 className="text-lg font-semibold text-[#004141] mb-4 pb-2 border-b border-gray-200 flex items-center">
                                <DocumentTextIcon className="h-6 w-6 mr-2 text-gray-600" /> Uploaded Documents
                            </h3>
                            {/* Use 'userData' to pass the view state to renderDocument */}
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                                {renderDocument(getNestedValue(userData, "commercialRegistration"), "Commercial Registration", "commercialRegistration")}
                                {renderDocument(getNestedValue(userData, "tradeLicense"), "Trade License", "tradeLicense")}
                                {renderDocument(getNestedValue(userData, "taxCard"), "Tax Card", "taxCard")}
                                {renderDocument(getNestedValue(userData, "establishmentCard"), "Establishment Card", "establishmentCard")}
                                {renderDocument(getNestedValue(userData, "bankStatement"), "Bank Statement", "bankStatement")}
                                {renderDocument(getNestedValue(userData, "auditedFinancialReport"), "Audited Report", "auditedFinancialReport")}
                                {renderDocument(getNestedValue(userData, "cashFlowLedger"), "Cash Flow", "cashFlowLedger")}
                                {renderDocument(getNestedValue(userData, "commercialCreditReport"), "CCR", "commercialCreditReport")}
                                {renderDocument(getNestedValue(userData, "memorandumOfAssociation"), "Memorandum of Association", "memorandumOfAssociation")}
                                {renderDocument(getNestedValue(userData, "articleOfAssociation"), "Article of Association", "articleOfAssociation")}
                                {renderDocument(getNestedValue(userData, "kyc.qatariId"), "Qatari ID", "kyc.qatariId")}
                                {renderDocument(getNestedValue(userData, "kyc.passport"), "Passport (User)", "kyc.passport")}
                                {renderDocument(getNestedValue(userData, "kyc.utilityBill"), "Utility Bill", "kyc.utilityBill")}
                                {renderDocument(getNestedValue(userData, "kyc.incomeDetails.proofOfIncome"), "Proof of Income", "kyc.incomeDetails.proofOfIncome")}
                                {renderDocument(getNestedValue(userData, "kyc.employmentDetails.employmentLetter"), "Employment Letter", "kyc.employmentDetails.employmentLetter")}
                                {renderDocument(getNestedValue(userData, "otherDocument"), "Other Document 1", "otherDocument")}
                                {renderDocument(getNestedValue(userData, "otherDocumentTwo"), "Other Document 2", "otherDocumentTwo")}
                                {renderDocument(getNestedValue(userData, "otherDocument3"), "Other Document 3", "otherDocument3")}
                                {renderDocument(getNestedValue(userData, "otherDocument4"), "Other Document 4", "otherDocument4")}
                                {renderDocument(getNestedValue(userData, "otherDocument5"), "Other Document 5", "otherDocument5")}
                                {renderDocument(getNestedValue(userData, "otherDocument6"), "Other Document 6", "otherDocument6")}
                                {renderDocument(getNestedValue(userData, "otherDocument7"), "Other Document 7", "otherDocument7")}
                                {renderDocument(getNestedValue(userData, "otherDocument8"), "Other Document 8", "otherDocument8")}
                                {renderDocument(getNestedValue(userData, "otherDocument9"), "Other Document 9", "otherDocument9")}
                                {renderDocument(getNestedValue(userData, "otherDocument10"), "Other Document 10", "otherDocument10")}
                            </div>
                        </section>

                        {/* 5. Shareholders */}
                        {(viewShareholders.length > 0 || isEditing) && ( // Show section if shareholders exist or if editing
                            <section>
                                <h3 className="text-lg font-semibold text-[#004141] mb-4 pb-2 border-b border-gray-200 flex items-center"><UsersIcon className="h-6 w-6 mr-2 text-gray-600" /> Shareholder Details</h3>
                                <div className="space-y-4">
                                    {/* Use formShareholders for mapping in edit mode to reflect changes */}
                                    {(isEditing ? formShareholders : viewShareholders).map((sh, index) => {
                                        // Get original data for view comparison or display
                                        const originalSh = viewShareholders[index] || {};
                                        const shareholderBasePath = `shareholders.${index}`; // Base path for this shareholder in userData/formData
                                        return (
                                            <div key={originalSh._id || `sh-${index}`} className={`p-4 border rounded-md shadow-sm ${isEditing ? 'bg-indigo-50 border-indigo-200' : 'bg-gray-50 border-gray-200'}`}>
                                                <p className="font-semibold text-gray-800 mb-2 border-b pb-1 text-base">Shareholder {index + 1}</p>
                                                <dl className="divide-y divide-gray-100">
                                                    {renderEditableDetailItem("First Name", getNestedValue(originalSh, "firstName"), `${shareholderBasePath}.firstName`)}
                                                    {renderEditableDetailItem("Middle Name", getNestedValue(originalSh, "middleName"), `${shareholderBasePath}.middleName`)}
                                                    {renderEditableDetailItem("Last Name", getNestedValue(originalSh, "lastName"), `${shareholderBasePath}.lastName`)}
                                                    {renderEditableDetailItem("Email", getNestedValue(originalSh, "email"), `${shareholderBasePath}.email`)}

                                                    {/* Pass original address for view, base path for edit */}
                                                    {renderEditableAddress(getNestedValue(originalSh, "address", {}), `${shareholderBasePath}.address`, "Shareholder Address")}
                                                    {/* Document Section */}
                                                    <div className="pt-2 space-y-2">
                                                        <h4 className="text-sm font-medium text-gray-600 pt-2">Documents</h4>
                                                        {/* Pass original doc data for view, use index for edit key */}
                                                        {renderDocument(getNestedValue(userData, `${shareholderBasePath}.passport`), "Passport", `${shareholderBasePath}.passport`, index)}
                                                        {renderDocument(getNestedValue(userData, `${shareholderBasePath}.qid`), "QID", `${shareholderBasePath}.qid`, index)}
                                                        {renderDocument(getNestedValue(userData, `${shareholderBasePath}.proofOfAddress`), "Proof of Address", `${shareholderBasePath}.proofOfAddress`, index)}
                                                    </div>
                                                </dl>
                                            </div>);
                                    })}
                                    {!isEditing && viewShareholders.length === 0 && (
                                        <p className="text-sm text-gray-500 italic px-4">No shareholder information provided.</p>
                                    )}
                                    {/* Add Shareholder button could go here if needed */}
                                </div>
                            </section>)}

                        {/* 6. Buyers */}
                        {(viewBuyers.length > 0 || isEditing) && (
                            <section>
                                <h3 className="text-lg font-semibold text-[#004141] mb-4 pb-2 border-b border-gray-200 flex items-center"><UsersIcon className="h-6 w-6 mr-2 text-gray-600" /> Buyer Details</h3>
                                <div className="space-y-4">
                                    {(isEditing ? formBuyers : viewBuyers).map((buyer, index) => {
                                        const originalBuyer = viewBuyers[index] || {};
                                        const buyerBasePath = `kyc.buyers.${index}`; // Base path assuming buyers are under kyc
                                        return (
                                            <div key={originalBuyer._id || `buyer-${index}`} className={`p-4 border rounded-md shadow-sm ${isEditing ? 'bg-indigo-50 border-indigo-200' : 'bg-gray-50 border-gray-200'}`}>
                                                <p className="font-semibold text-gray-800 mb-2 border-b pb-1 text-base">Buyer {index + 1}</p>
                                                <dl className="divide-y divide-gray-100">
                                                    {renderEditableBuyerItem("Name", getNestedValue(originalBuyer, "buyerName"), 'kyc.buyers', index, 'buyerName')}
                                                    {renderEditableBuyerItem("CRN / Registration", getNestedValue(originalBuyer, "registrationNumber"), 'kyc.buyers', index, 'registrationNumber')}
                                                    {renderEditableBuyerItem("Contact Person", getNestedValue(originalBuyer, "contactPerson"), 'kyc.buyers', index, 'contactPerson')}
                                                    {renderEditableBuyerItem("Contact Phone", getNestedValue(originalBuyer, "contactPhone"), 'kyc.buyers', index, 'contactPhone')}
                                                    {renderEditableBuyerItem("Contact Email", getNestedValue(originalBuyer, "contactEmail"), 'kyc.buyers', index, 'contactEmail', 'email')}
                                                    {/* Example for potential future fields */}
                                                    {/* {renderEditableBuyerItem("Business Type", getNestedValue(originalBuyer, "businessType"), 'kyc.buyers', index, 'businessType')} */}
                                                    {/* {renderEditableBuyerItem("Annual Purchase Volume", getNestedValue(originalBuyer, "annualPurchaseVolume"), 'kyc.buyers', index, 'annualPurchaseVolume', 'number')} */}
                                                    {/* {renderEditableBuyerItem("Payment Terms", getNestedValue(originalBuyer, "paymentTerms"), 'kyc.buyers', index, 'paymentTerms')} */}
                                                    {/* {renderEditableAddress(getNestedValue(originalBuyer, "address", {}), `${buyerBasePath}.address`, "Buyer Address", true)} */} {/* Assuming address object */}
                                                    {/* Document for Buyer - Note: Sample data has doc at buyer level but structure might vary */}
                                                    {/* <div className="pt-3">
                                                        <h4 className="text-sm font-medium text-gray-600 mb-1">Company Document</h4>
                                                        {renderDocument(getNestedValue(userData, `${buyerBasePath}.companyDocument`), "Company Document", `${buyerBasePath}.companyDocument`, index)}
                                                        {isEditing && <p className="text-xs text-center pt-1 text-gray-500 italic">Buyer document management not available here.</p>}
                                                     </div> */}
                                                </dl>
                                            </div>
                                        );
                                    })}
                                    {!isEditing && viewBuyers.length === 0 && (
                                        <p className="text-sm text-gray-500 italic px-4">No buyer information provided.</p>
                                    )}
                                    {/* Add Buyer button could go here */}
                                </div>
                            </section>
                        )}

                        {/* 7. Directors (Display Only Section) */}
                        {viewDirectors.length > 0 && (
                            <section>
                                <h3 className="text-lg font-semibold text-[#004141] mb-4 pb-2 border-b border-gray-200 flex items-center"><UsersIcon className="h-6 w-6 mr-2 text-gray-600" /> Directors</h3>
                                <div className={`p-4 rounded-md border divide-y divide-gray-100 ${isEditing ? 'bg-gray-100 border-gray-300' : 'bg-gray-50 border-gray-200'}`}>
                                    {viewDirectors.map((dir, index) => (
                                        <div key={dir._id || `dir-${index}`} className="py-2">
                                            {/* Assuming director fields like directorName, position, isUBO, contactEmail/Phone exist */}
                                            <p className="text-sm font-medium text-gray-900">{getNestedValue(dir, "directorName", 'N/A')}</p>
                                            <p className="text-xs text-gray-500">
                                                {getNestedValue(dir, "position", 'Position N/A')} | UBO: {getNestedValue(dir, "isUBO") ? 'Yes' : 'No'} | {getNestedValue(dir, "contactEmail") || getNestedValue(dir, "contactPhone") || 'No Contact Info'}
                                            </p>
                                        </div>
                                    ))}
                                    {isEditing && <p className="text-xs text-center pt-2 text-gray-500 italic">Director information cannot be edited here.</p>}
                                </div>
                            </section>)}

                        {/* Save Button Area - Only visible in edit mode */}
                        {isEditing && (
                            <div className="mt-8 pt-5 border-t border-gray-200 flex justify-end">
                                <button
                                    type="submit" // Submit the form
                                    disabled={isSaving || Object.values(uploadingDoc).some(v => v === true)}
                                    className="inline-flex items-center justify-center px-6 py-2 border border-transparent text-base font-medium rounded-md shadow-sm text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed"
                                >
                                    {isSaving ? (
                                        <> <ArrowPathIcon className="h-5 w-5 mr-2 animate-spin" /> Saving... </>
                                    ) : (
                                        <> <CheckIcon className="h-5 w-5 mr-2" /> Save Changes </>
                                    )}
                                </button>
                            </div>
                        )}

                    </div>
                </form>


                {/* Document Viewer Modal */}
                <Modal
                    isOpen={isDocViewerOpen}
                    onRequestClose={closeDocumentViewer}
                    contentLabel="Document Viewer"
                    ariaHideApp={false} // Set properly in real app
                    style={{
                        overlay: { backgroundColor: 'rgba(0, 0, 0, 0.75)', zIndex: 1050 },
                        content: {
                            top: '5%', left: '5%', right: '5%', bottom: '5%',
                            padding: '0', border: 'none', borderRadius: '8px',
                            display: 'flex', flexDirection: 'column', overflow: 'hidden', background: '#fff'
                        }
                    }}
                >
                    <div className="flex justify-between items-center p-3 bg-gray-100 border-b rounded-t-lg flex-shrink-0">
                        <h2 className="text-lg font-semibold text-gray-800">Document Preview</h2>
                        <button onClick={closeDocumentViewer} className="text-gray-500 hover:text-gray-800 p-1 rounded-full hover:bg-gray-200">
                            <XMarkIcon className="h-6 w-6" />
                        </button>
                    </div>
                    <div className="flex-grow overflow-hidden bg-gray-200">
                        {docViewerUrl ? (
                            <iframe src={docViewerUrl} width="100%" height="100%" title="Document View" frameBorder="0" />
                        ) : (
                            <p className="p-10 text-center text-gray-500">Could not load document URL.</p>
                        )}
                    </div>
                </Modal>
                {/* Consent Modal is removed as per original code comment */}

            </div>
        </div>
    );
};

// Set app element for react-modal accessibility if needed
// Make sure this runs only in the browser environment
// if (typeof window !== 'undefined') {
//    const rootElement = document.getElementById('root'); // Or your app's root element ID
//    if (rootElement) {
//       Modal.setAppElement(rootElement);
//    } else {
//        console.warn('React Modal: App element #root not found for accessibility.');
//    }
// }

export default Profile;