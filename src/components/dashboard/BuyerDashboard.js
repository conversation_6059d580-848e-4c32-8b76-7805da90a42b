import React, { useState, useEffect } from 'react';
import { useHistory } from 'react-router-dom';
import { useUser } from '../../contexts/UserContext';
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';
import { format, parseISO, differenceInDays } from 'date-fns';
import StatusBadge from '../StatusBadge'; // Ensure this path is correct
import config from '../../config';
import SharedCache from '../../sharedCache';
import { ArrowDownIcon } from '@heroicons/react/16/solid';
import { ArrowDownOnSquareIcon } from '@heroicons/react/24/outline';
import axios from 'axios';
import LoadingModal from '../Reusable/Loading';

export default function BuyerDashboard() {
    const history = useHistory();
    const { isDualRoleUser, userType } = useUser();
    const [buyerName, setBuyerName] = useState('');
    const [invoices, setInvoices] = useState([]);
    const [error, setError] = useState(null);
    const [loading, setLoading] = useState(true);
    const [buyerLenderName, setBuyerLenderName] = useState('');
    const [isFetching, setIsFetching] = useState(true); // Loading for initial table fetch
    const [fetchError, setFetchError] = useState(null); // Error message during fetch

    useEffect(() => {
        const storedBuyerData = SharedCache.get('buyerFullData');
        if (storedBuyerData && storedBuyerData.lenderName) {
            setBuyerLenderName(storedBuyerData.lenderName);
            setBuyerName(storedBuyerData.lenderName);
        } else {
            setBuyerName("Buyer");
            console.warn("Buyer name not found in SharedCache or missing lenderName");
            setFetchError("Buyer profile data missing. Please try logging in again.");
            setIsFetching(false);
        }
    }, []);

    useEffect(() => {
        const fetchInvoices = async () => {
            if (!buyerLenderName) return;
            setIsFetching(true);
            try {
                const response = await axios.post(`${config.apiUrl}/ops/invoiceFinancing/fetchInvoicesForBuyer`, {
                    buyerAdminLenderName: buyerLenderName
                }, {
                    headers: { 'Content-Type': 'application/json' }
                });

                if (Array.isArray(response.data)) {
                    const sorted = response.data
                        .map(i => ({ ...i, totalAmount: parseFloat(i.totalAmount) }))
                        .sort(/* sorting logic */);
                    setInvoices(sorted);
                } else {
                    setFetchError(response.data?.message || "Unexpected response");
                }
            } catch (err) {
                setFetchError(err.response?.data?.message || err.message);
            } finally {
                setIsFetching(false);
            }
        };

        fetchInvoices();
    }, [buyerLenderName]);


    const totalInvoices = invoices.length;
    const pendingApprovals = invoices.filter((invoice) =>
        ["PENDING_BUYER_APPROVAL", "VERIFICATION_PENDING_ANCHOR"].includes(invoice.status)
    ).length;
    const approvedInvoices = invoices.filter((invoice) =>
        ["APPROVED_BY_BUYER", "VERIFIED_ANCHOR"].includes(invoice.status)
    ).length;
    const rejectedInvoices = invoices.filter((invoice) =>
        ["REJECTED_BY_BUYER", "REJECTED_ANCHOR"].includes(invoice.status)
    ).length;

    // Helper function to safely parse date strings
    const safeParseDate = (dateString) => {
        if (!dateString) return null; // Return null if dateString is undefined or null
        try {
            const parsed = parseISO(dateString);
            // Check if parsed date is valid (e.g., if parseISO couldn't parse it)
            if (isNaN(parsed.getTime())) return null;
            return parsed;
        } catch (e) {
            console.error("Error parsing date:", dateString, e);
            return null;
        }
    };

    const monthlyStatusData = invoices.reduce((acc, invoice) => {
        const createdAtDate = safeParseDate(invoice.createdAt);
        if (!createdAtDate) return acc;

        const key = format(createdAtDate, 'MMM yyyy'); // e.g., "Feb 2025"

        if (!acc[key]) {
            acc[key] = {
                name: key,
                total: 0,
                approved: 0,
                pending: 0,
                rejected: 0,
            };
        }

        acc[key].total += 1;

        if (["APPROVED_BY_BUYER", "VERIFIED_ANCHOR"].includes(invoice.status)) {
            acc[key].approved += 1;
        } else if (["PENDING_BUYER_APPROVAL", "VERIFICATION_PENDING_ANCHOR"].includes(invoice.status)) {
            acc[key].pending += 1;
        } else if (["REJECTED_BY_BUYER", "REJECTED_ANCHOR"].includes(invoice.status)) {
            acc[key].rejected += 1;
        }

        return acc;
    }, {});

    // Prepare data for the monthly invoice status chart
    const monthlyData = invoices.reduce((acc, invoice) => {
        const createdAtDate = safeParseDate(invoice.createdAt || invoice.invoiceDate);
        if (createdAtDate) {
            const month = format(createdAtDate, 'MMM');
            acc[month] = (acc[month] || 0) + 1;
        }
        return acc;
    }, {});

    const chartMonths = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
    const chartOverviewData = chartMonths.map(month => ({
        name: month,
        "Total Invoices": monthlyData[month] || 0,
    }));

    const latestInvoices = invoices
        .sort((a, b) => {
            const dateA = safeParseDate(a.createdAt);
            const dateB = safeParseDate(b.createdAt);
            if (!dateA || !dateB) return 0; // Cannot sort if dates are invalid
            return dateB.getTime() - dateA.getTime();
        })
        .slice(0, 5)
        .map(invoice => {
            const invoiceDate = safeParseDate(invoice.invoiceDate);
            const ageInDays = invoiceDate ? differenceInDays(new Date(), invoiceDate) : 'N/A';

            return {
                invoiceId: invoice.invoiceNumber,
                invoiceDate: invoiceDate ? format(invoiceDate, 'dd MMM, yyyy') : 'N/A',
                age: ageInDays !== 'N/A' ? `${ageInDays} days ago` : 'N/A',
                buyer: invoice.customerName,
                tradeLegalName: invoice.supplierName || 'N/A',
                invoiceAmount: typeof invoice.totalAmount === 'number'
                    ? parseFloat(invoice.totalAmount).toLocaleString('en-IN', { style: 'currency', currency: 'INR' })
                    : 'N/A', // Handle non-numeric totalAmount
                status: invoice.status,
                fullInvoice: invoice
            };
        });

    const WelcomeCard = ({ userName }) => { // Removed isDualRoleUser as it's not used here directly for rendering
        return (
            <div className="p-6 rounded-lg flex items-center justify-between">
                <div className="flex items-center">
                    <div>
                        <h1 className="text-xl font-bold text-gray-800">Welcome, {userName}!</h1>
                        <p className="text-gray-600">Here's your summary as of {format(new Date(), 'MMMM dd, yyyy')}</p>
                    </div>
                </div>
                <div className="flex items-center space-x-4">
                    <button className="flex items-center px-4 py-2 rounded-md bg-white border border-gray-300 text-gray-700 hover:bg-gray-100">
                        Export
                        <ArrowDownOnSquareIcon className="w-5 h-5 ml-2" />
                    </button>
                    <select className="px-4 py-2 rounded-md bg-white border border-gray-300 text-gray-700">
                        <option>This Year</option>
                        <option>Last 30 Days</option>
                        <option>Last 7 Days</option>
                    </select>
                </div>
            </div>
        );
    };

    return (
        <div className="p-8 bg-gray-50 min-h-screen">
            {isFetching && (<LoadingModal />)}

            {error && <div className="text-red-600 bg-red-100 p-4 rounded mb-4">Error: {error}</div>}

            <WelcomeCard userName={buyerName} />

            <div className="grid grid-cols-1 md:grid-cols-12 gap-6 mb-8">
                {isDualRoleUser && (
                    <div className="md:col-span-3 bg-[#feb368] p-6 rounded-lg shadow-md self-start ">
                        <div className="mb-4">
                            <img src={require("../../images/switches.png")} alt="Toggle Graphic" className="w-24 h-auto mx-auto mb-4" />
                        </div>
                        <h3 className="text-xl font-bold text-white text-center mb-2">Buyers Can Also Unlock Cash with MSME Status</h3>
                        <p className="text-sm text-white text-center">
                            As a buyer, you can register as an MSME and access instant liquidity through invoice discounting.
                            Convert your payables into opportunities—free up working capital and strengthen your cash flow, just
                            like suppliers do.
                        </p>
                    </div>
                )}
                <div className={isDualRoleUser ? "md:col-span-9" : "md:col-span-12"}>
                    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                        <div className="bg-[#e1f2f5] shadow-md rounded-lg p-6 text-center">
                            <p className="text-lg font-semibold text-gray-800">Total Invoices</p>
                            <p className="text-4xl font-bold text-gray-700">{totalInvoices}</p>
                        </div>
                        <div className="bg-white shadow-md rounded-lg p-6 text-center">
                            <p className="text-lg font-semibold text-gray-800">Pending Approvals</p>
                            <p className="text-4xl font-bold text-[#facc15]">{pendingApprovals}</p>
                        </div>
                        <div className="bg-white shadow-md rounded-lg p-6 text-center">
                            <p className="text-lg font-semibold text-gray-800">Approved Invoices</p>
                            <p className="text-4xl font-bold text-[#22c55e]">{approvedInvoices}</p>
                        </div>
                        <div className="bg-white shadow-md rounded-lg p-6 text-center">
                            <p className="text-lg font-semibold text-gray-800">Rejected Invoices</p>
                            <p className="text-4xl font-bold text-[#ef4444]">{rejectedInvoices}</p>
                        </div>
                    </div>
                    <div className="bg-white shadow-md rounded-lg p-6 mb-8 h-[350px]">
                        <h3 className="text-xl font-semibold text-gray-800 mb-4">Invoices Status Overview</h3>
                        <ResponsiveContainer width="100%" height="85%">
                            <BarChart data={chartOverviewData}>
                                <CartesianGrid strokeDasharray="3 3" vertical={false} />
                                <XAxis dataKey="name" />
                                <YAxis />
                                <Tooltip />
                                <Bar dataKey="Total Invoices" fill="#004141" />
                            </BarChart>
                        </ResponsiveContainer>
                    </div>
                </div>
            </div>

            <div className="bg-white shadow-md rounded-lg p-6 mb-8">
                <div className="flex justify-between items-center mb-4">
                    <h3 className="text-xl font-semibold text-gray-800">Recently added invoices</h3>
                    <button onClick={() => history.push('/invoiceApprovals')} className="text-blue-600 hover:underline text-sm">View All</button>
                </div>
                <div className="overflow-x-auto">
                    <table className="min-w-full divide-y divide-gray-200">
                        <thead className="bg-gray-50">
                            <tr>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Invoice ID</th>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Invoice Date</th>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Age</th>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Supplier</th>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Invoice Amount</th>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                            </tr>
                        </thead>
                        <tbody className="bg-white divide-y divide-gray-200">
                            {latestInvoices.length > 0 ? (
                                latestInvoices.map((invoice, index) => (
                                    <tr key={index}>
                                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{invoice.invoiceId}</td>
                                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{invoice.invoiceDate}</td>
                                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{invoice.age}</td>
                                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{invoice.tradeLegalName}</td>
                                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{invoice.invoiceAmount}</td>
                                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            <StatusBadge status={invoice.status} />
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                            <button onClick={() => history.push(`/buyer-invoices`)} className="text-blue-600 hover:text-blue-900">
                                                View Detail
                                            </button>
                                        </td>
                                    </tr>
                                ))
                            ) : (
                                <tr>
                                    <td colSpan="7" className="px-6 py-4 text-center text-sm text-gray-500">No recent invoices found.</td>
                                </tr>
                            )}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    );
}