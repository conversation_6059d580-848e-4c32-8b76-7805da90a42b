import React, { useState, useEffect, useContext } from 'react';
import {
  CheckCircleIcon,
  UserCircleIcon,
  ChartBarIcon,
  ClipboardDocumentListIcon, // Used for My Invoices/Invoices for Approval
  BanknotesIcon,
  UserGroupIcon, // For My Buyers (MSME only)
  Cog6ToothIcon,
  ChatBubbleOvalLeftEllipsisIcon,
} from '@heroicons/react/24/outline';
import { useLocation, useHistory } from 'react-router-dom';
import axios from 'axios';
import { LayoutContext } from '../../MainLayout';
import config from '../../config';
import SharedCache from '../../sharedCache';
import { useUser } from '../../contexts/UserContext';
import Lottie from 'lottie-react';
import loadingAnimation from "../../assets/loading.json"

const Sidebar = () => {
  const { currentPage, setCurrentPage } = useContext(LayoutContext);
  const { userType, isBuyer, isMsme } = useUser();
  const location = useLocation();
  const history = useHistory();

  const [userId, setUserId] = useState(null);
  const [userVerificationStatus, setUserVerificationStatus] = useState(null);
  const [creditLineStatus, setCreditLineStatus] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // MSME specific dashboard items based on credit line status
  const getMsmeDashboardItems = () => {
    const baseItems = [
      { name: 'Overview', icon: ChartBarIcon, page: 'dashboard', path: '/dashboard' }
    ];

    if (creditLineStatus === 'ACTIVE') {
      return [
        ...baseItems,
        { name: 'My Buyers', icon: UserGroupIcon, page: 'my-buyers', path: '/dashboard' },
        { name: 'My Invoices', icon: ClipboardDocumentListIcon, page: 'my-invoices', path: '/dashboard' },
        { name: 'Payments and Disburses', icon: BanknotesIcon, page: 'payments', path: '/dashboard' },
        { name: 'Profile', icon: Cog6ToothIcon, page: 'settings', path: '/dashboard' },
      ];
    }

    return baseItems;
  };

  // Buyer specific dashboard items
  const getBuyerDashboardItems = () => {
    return [
      { name: 'Buyer Dashboard', icon: ChartBarIcon, page: 'buyer-dashboard', path: '/buyer-dashboard' },
      { name: 'Invoices for Approval', icon: ClipboardDocumentListIcon, page: 'invoices-approval', path: '/buyer-invoices' }, // Path changed!
      // Add other buyer-specific items as needed
    ];
  };

  const shouldShowMsmeOnboardingSections = () => {
    return userVerificationStatus === 'INITIATED' || userVerificationStatus === 'REINITIATED';
  };

  const fetchUserData = async (uid) => {
    try {
      setLoading(true); // Still show loading if fetching MSME data
      setError(null);

      // Fetch MSME user details
      const userResponse = await axios.get(`${config.apiUrl}/ops/invoiceFinancing/users/id/${uid}`);
      const userData = userResponse.data;
      console.log(userResponse, userData, "asdadadasd")

      if (userResponse.status === 200 && userData?.firstName) { // Check for successful response and data
        if (userData?.kyc.verificationStatus) {
          setUserVerificationStatus(userData.kyc.verificationStatus);
        }

        // ONLY FETCH CREDIT LINE DATA IF verificationStatus IS NOT 'INITIATED' OR 'REINITIATED'
        // This is the core change to prevent fetching credit line when verification is pending.
        if (userData?.kyc.verificationStatus !== 'INITIATED' && userData?.kyc.verificationStatus !== 'REINITIATED') {
          // Fetch credit line status
          const creditLineResponse = await axios.get(
            `${config.apiUrl}/ops/invoiceFinancing/fetchCreditLine/${uid}`
          );

          if (creditLineResponse.status === 200 && creditLineResponse.data?.creditLineStatus) {
            setCreditLineStatus(creditLineResponse.data.creditLineStatus);
          } else {
            console.warn("Failed to fetch MSME credit line status:", creditLineResponse.data);
            setCreditLineStatus('INACTIVE'); // Default to inactive on non-OK response
          }
        } else {
          // If verificationStatus is INITIATED or REINITIATED, ensure creditLineStatus is not set
          // to avoid showing dashboard items prematurely.
          setCreditLineStatus(null); // Explicitly set to null or 'INACTIVE' if you prefer
        }
      } else {
        console.warn("Failed to fetch MSME user details or data missing:", userData);
        setError('MSME user data not found or invalid.');
        setUserVerificationStatus('INITIATED'); // Default states on data issue
        setCreditLineStatus(null); // Ensure creditLineStatus is not set
      }
    } catch (err) {
      console.error('Error fetching MSME specific user data:', err);
      setError('Failed to load MSME data');
      setUserVerificationStatus('INITIATED');
      setCreditLineStatus(null); // Ensure creditLineStatus is not set on error
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    const userFromCache = SharedCache.get('user');
    if (userFromCache && (userFromCache._id || userFromCache.id)) {
      const uid = userFromCache._id || userFromCache.id;
      setUserId(uid);

      if (isMsme) {
        // Only fetch MSME specific data if current userType is MSME
        fetchUserData(uid);
      } else {
        // If it's a buyer, no need to fetch MSME data
        setLoading(false); // Indicate loading is complete for buyer
        setError(null); // Clear any old errors
        // Reset MSME specific states
        setUserVerificationStatus(null);
        setCreditLineStatus(null);
      }
    } else {
      setLoading(false);
      setError('User not found in cache');
      setUserId(null); // Clear userId if user not found
      setUserVerificationStatus(null);
      setCreditLineStatus(null);
    }
  }, [isMsme]); // Rerun if isMsme changes


  const handleNavigate = (page, pathOverride = null) => {
    if (page === 'onboarding') {
      history.push('/kyc/personal-info');
      setCurrentPage('onboarding');
    } else if (page === 'eligibility-check') {
      history.push('/eligibility-checker');
      setCurrentPage('eligibility-check');
    } else if (page === 'support') {
      history.push('/support');
      setCurrentPage('support');
    } else if (page === 'invoices-approval') { // Specific for Buyer's Invoices
      history.push('/buyer-invoices');
      setCurrentPage('invoices-approval');
    }
    else if (page === 'buyer-dashboard') { // Specific for Buyer's Dashboard
      history.push('/buyer-dashboard');
      setCurrentPage('buyer-dashboard');
    }
    else {
      // For MSME dashboard and its sub-pages
      history.push(pathOverride || '/dashboard');
      setCurrentPage(page);
    }
  };

  const handleLogoClick = () => {
    if (isBuyer) {
      history.push('/buyer-dashboard');
      setCurrentPage('buyer-dashboard');
    } else {
      history.push('/dashboard');
      setCurrentPage('dashboard');
    }
  };

  const activeTabTextClass = 'text-[#edf5f7]';
  const activeTabBackgroundClass = 'bg-[#004141]';
  const inactiveTextClass = 'text-[#004141]';
  const inactiveHoverBgClass = 'hover:bg-[#d8e6e9]';
  const inactiveHoverTextClass = 'hover:text-[#003737]';

  if (loading && isMsme) {
    return (
      <aside className="bg-[#edf5f7] p-4 flex flex-col h-full w-full sticky top-0">
        <div className="flex items-center justify-start flex-shrink-0 px-1">
          <img
            src={require("../../images/logo.jpg")}
            alt="Logo"
            className="h-16 md:h-24 w-auto object-contain cursor-pointer"
            onClick={handleLogoClick}
          />
        </div>
        <div className="flex-grow flex items-center justify-center">
          <Lottie
            animationData={loadingAnimation}
            loop
            className="w-36 h-36 md:w-42 md:h-42 mx-auto"
          />        </div>
      </aside>
    );
  }

  if (error && isMsme) {
    return (
      <aside className="bg-[#edf5f7] p-4 flex flex-col h-full w-full sticky top-0">
        <div className="flex items-center justify-start flex-shrink-0 px-1">
          <img
            src={require("../../images/logo.jpg")}
            alt="Logo"
            className="h-16 md:h-24 w-auto object-contain cursor-pointer"
            onClick={handleLogoClick}
          />
        </div>
        <div className="flex-grow flex items-center justify-center">
          <div className="text-red-600 text-sm text-center">
            <p>Unable to load MSME data</p>
            <button
              onClick={() => window.location.reload()}
              className="mt-2 text-[#004141] hover:underline"
            >
              Retry
            </button>
          </div>
        </div>
      </aside>
    );
  }

  return (
    <aside className="bg-[#edf5f7] p-4 flex flex-col h-full w-full sticky top-0">
      <div className="flex items-center justify-start flex-shrink-0 px-1">
        <img
          src={require("../../images/logo.jpg")}
          alt="Logo"
          className="h-16 md:h-24 w-auto object-contain cursor-pointer"
          onClick={handleLogoClick}
        />
      </div>

      <nav className="space-y-2 flex-grow overflow-y-auto mt-4 custom-scrollbar">
        {isMsme && !shouldShowMsmeOnboardingSections() && getMsmeDashboardItems().map((item) => (
          <button
            key={item.page}
            className={`flex items-center w-full text-left p-3 rounded-md transition-colors ${currentPage === item.page
              ? `${activeTabBackgroundClass} ${activeTabTextClass}`
              : `${inactiveTextClass} ${inactiveHoverBgClass} ${inactiveHoverTextClass}`
              }`}
            onClick={() => handleNavigate(item.page, item.path)}
          >
            <item.icon className="h-6 w-6 mr-3 flex-shrink-0" />
            <span className="text-sm font-medium">{item.name}</span>
          </button>
        ))}

        {isBuyer && getBuyerDashboardItems().map((item) => (
          <button
            key={item.page}
            className={`flex items-center w-full text-left p-3 rounded-md transition-colors ${currentPage === item.page
              ? `${activeTabBackgroundClass} ${activeTabTextClass}`
              : `${inactiveTextClass} ${inactiveHoverBgClass} ${inactiveHoverTextClass}`
              }`}
            onClick={() => handleNavigate(item.page, item.path)}
          >
            <item.icon className="h-6 w-6 mr-3 flex-shrink-0" />
            <span className="text-sm font-medium">{item.name}</span>
          </button>
        ))}

        {isMsme && shouldShowMsmeOnboardingSections() && (
          <>
            <button
              className={`flex items-center w-full text-left p-3 rounded-md transition-colors ${currentPage === 'eligibility-check'
                ? `${activeTabBackgroundClass} ${activeTabTextClass}`
                : `${inactiveTextClass} ${inactiveHoverBgClass} ${inactiveHoverTextClass}`
                }`}
              onClick={() => handleNavigate('eligibility-check')}
            >
              <CheckCircleIcon className="h-6 w-6 mr-3 flex-shrink-0" />
              <span className="text-sm font-medium">Eligibility Check</span>
            </button>

            <button
              className={`flex items-center w-full text-left p-3 rounded-md transition-colors ${currentPage === 'onboarding'
                ? `${activeTabBackgroundClass} ${activeTabTextClass}`
                : `${inactiveTextClass} ${inactiveHoverBgClass} ${inactiveHoverTextClass}`
                }`}
              onClick={() => handleNavigate('onboarding')}
            >
              <UserCircleIcon className="h-6 w-6 mr-3 flex-shrink-0" />
              <span className="text-sm font-medium">Onboarding</span>
            </button>
          </>
        )}
      </nav>

      <div className="my-3 flex-shrink-0">
        <hr className="border-t border-[#c0d0d3]" />
      </div>

      <nav className="flex-shrink-0 pb-4">
        <button
          className={`flex items-center w-full text-left p-3 rounded-md transition-colors ${currentPage === 'support'
            ? `${activeTabBackgroundClass} ${activeTabTextClass}`
            : `${inactiveTextClass} ${inactiveHoverBgClass} ${inactiveHoverTextClass}`
            }`}
          onClick={() => handleNavigate('support')}
        >
          <ChatBubbleOvalLeftEllipsisIcon className="h-6 w-6 mr-3 flex-shrink-0" />
          <span className="text-sm font-medium">Help and Support</span>
        </button>
      </nav>
    </aside>
  );
};

export default Sidebar;