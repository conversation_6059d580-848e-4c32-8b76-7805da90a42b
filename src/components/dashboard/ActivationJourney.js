// src/components/dashboard/ActivationJourney.js
import React, { useState, useEffect } from 'react';
import { useHistory } from "react-router-dom";
import axios from 'axios';
import config from '../../config.json';
import SharedCache from '../../sharedCache';
import { uploadKycDocument, uploadShareholderDocument } from '../../api/kyc'; // <-- Import BOTH
import { generateRevolvingCreditPdf } from './CreditLineContract'; // Import the function
import { shortenDocumentName } from '../../components/utils'; // Import the utility function
import {
    CheckCircleIcon,  // For APPROVED
    ExclamationCircleIcon as ExclamationIcon,
    ClockIcon,
    UserGroupIcon,
    ExclamationCircleIcon,
    UserPlusIcon,
    UserIcon,
    UserCircleIcon,
    InformationCircleIcon,
    EyeIcon
} from '@heroicons/react/24/outline';
import SalorTelescope from '../../images/salor_telescope.png'; // NEW IMPORT
import LoadingModal from '../Reusable/Loading';
// --- Helper Functions ---
const formatCurrency = (amount) => {
    const num = Number(amount);
    return !isNaN(num) ? `QAR ${num.toLocaleString()}` : 'QAR 0';
};

// Add this helper function somewhere accessible by ActivationJourney, maybe above it.
const getNested = (obj, path, defaultValue = undefined) => { // Default to undefined to differentiate missing from actual null/empty
    try {
        const value = path.split('.').reduce((o, k) => (o || {})[k], obj);
        return value === undefined ? defaultValue : value;
    } catch (e) {
        console.error("Error in getNested:", e, "Path:", path);
        return defaultValue;
    }
};


// Add this new component within ActivationJourney.js or import it

const UploadIcon = () => <svg className="w-4 h-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12"></path></svg>;

const RejectedDocumentItem = ({ docInfo, userId, token, onReuploadSuccess }) => {
    const [isUploading, setIsUploading] = useState(false);
    const [uploadError, setUploadError] = useState(null);
    const fileInputRef = React.useRef(null);

    const handleFileChange = (event) => {
        const file = event.target.files[0];
        if (file) {
            handleUpload(file);
        }
        if (fileInputRef.current) {
            fileInputRef.current.value = "";
        }
    };

    const handleUpload = async (file) => {
        setIsUploading(true);
        setUploadError(null);
        console.log(`Attempting to re-upload for type: ${docInfo.type}, path: ${docInfo.path}, index: ${docInfo.arrayIndex}`);

        let uploadResponse = null; // To store response from either API

        try {
            // --- API Call 1: Upload Document (Conditional Logic) ---

            if (docInfo.type?.startsWith('shareholder_')) {
                // --- Use Shareholder Specific API ---
                const shareholderIndex = docInfo.arrayIndex;
                const shareholderDocKey = docInfo.type.replace('shareholder_', ''); // Extract 'passport', 'qid', etc.

                if (shareholderIndex === undefined || shareholderIndex === null) {
                    throw new Error(`Missing shareholder index for path ${docInfo.path}`);
                }
                if (!['passport', 'qid', 'proofOfAddress'].includes(shareholderDocKey)) {
                    throw new Error(`Invalid shareholder document key derived: ${shareholderDocKey}`);
                }

                console.log(`Calling uploadShareholderDocument: userId=${userId}, index=${shareholderIndex}, key=${shareholderDocKey}`);
                uploadResponse = await uploadShareholderDocument(userId, shareholderIndex, shareholderDocKey, file);

            } else if (docInfo.type === 'directorIdDocument' && docInfo.arrayIndex !== undefined) {
                // --- Handle Director Doc Upload (Placeholder - Needs dedicated API or adjustment) ---
                console.warn("Director document re-upload requires a specific API call (e.g., uploadDirectorDocument) or modification to uploadKycDocument to handle index/ID. Using generic upload for now, which might fail.");
                // For now, attempt generic upload - THIS MAY NEED ADJUSTMENT based on backend
                const documentTypeKey = docInfo.type; // e.g., 'directorIdDocument'
                // Might need to pass index separately if generic API supports it
                // formData.append('arrayIndex', docInfo.arrayIndex); // Example if needed
                console.log(`Calling generic uploadKycDocument for Director: userId=${userId}, type=${documentTypeKey}`);
                uploadResponse = await uploadKycDocument(userId, documentTypeKey, file); // Use the imported API function

            } else {
                // --- Use Generic KYC Upload API ---
                // Use the mapped type key for the generic API
                const documentTypeKey = docInfo.type;
                if (!documentTypeKey) {
                    throw new Error(`Missing document type mapping for path ${docInfo.path}`);
                }
                console.log(`Calling generic uploadKycDocument: userId=${userId}, type=${documentTypeKey}`);
                uploadResponse = await uploadKycDocument(userId, documentTypeKey, file); // Use the imported API function
            }

            // --- Process Upload Response ---
            if (!uploadResponse || !uploadResponse.success) {
                // Use message from response if available
                throw new Error(uploadResponse?.message || uploadResponse?.error || 'Upload failed');
            }
            // Optional: Check for uploadResponse.documentData if needed later

            console.log(`Upload successful for ${docInfo.path}. Updating status...`);

            // --- API Call 2: Update Status to SUBMITTED (Remains the same) ---
            const statusUpdateResponse = await axios.post(
                `${config.apiUrl}/ops/invoiceFinancing/updateDocumentStatus`,
                {
                    userId: userId,
                    documentFieldPath: docInfo.path, // The path correctly identifies the doc location
                    verificationStatus: 'SUBMITTED',
                    verificationNotes: '' // Clear rejection notes
                },
                { headers: { 'x-auth-token': token } }
            );

            if (!statusUpdateResponse.data || !statusUpdateResponse.data.success) {
                console.error("Status update failed after successful upload:", statusUpdateResponse.data.message);
                throw new Error(statusUpdateResponse.data.message || 'Status update failed after upload');
            }

            console.log(`Status updated to SUBMITTED for ${docInfo.path}`);
            onReuploadSuccess(docInfo.path); // Notify parent

        } catch (error) {
            console.error(`Error re-uploading document ${docInfo.path}:`, error);
            const message = error.response?.data?.message || error.response?.data?.error || error.message || 'Re-upload failed.';
            setUploadError(message);
        } finally {
            setIsUploading(false);
        }
    };

    // Extract filename from path if available
    const getFilenameFromPath = (path) => {
        if (!path) return '';
        const parts = path.split('/');
        return parts[parts.length - 1] || '';
    };

    // Get document filename from path or use a default
    const documentFilename = getFilenameFromPath(docInfo.filePath || '');
    // Shorten the document name for display
    const shortDocName = documentFilename ? shortenDocumentName(documentFilename) : '';

    return (
        <div className="p-3 border border-red-200 rounded-md bg-red-50 text-sm">
            <input
                type="file"
                ref={fileInputRef}
                onChange={handleFileChange}
                style={{ display: 'none' }}
                accept=".pdf,.jpg,.jpeg,.png" // Example accept types
            />
            <div className="flex justify-between items-start">
                <p className="font-semibold text-gray-800">{docInfo.label}</p>
                {shortDocName && (
                    <span className="text-xs bg-white px-2 py-1 rounded border border-gray-200 ml-2" title={documentFilename}>
                        {shortDocName}
                    </span>
                )}
            </div>
            <p className="text-red-700 mt-1">Reason: <span className="italic">{docInfo.notes}</span></p>
            {uploadError && <p className="text-red-600 text-xs mt-1">Error: {uploadError}</p>}
            <button
                onClick={() => fileInputRef.current?.click()}
                disabled={isUploading}
                className={`mt-2 inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded shadow-sm text-white focus:outline-none focus:ring-2 focus:ring-offset-1 transition duration-150 ease-in-out ${isUploading ? 'bg-gray-400 cursor-not-allowed' : 'bg-blue-600 hover:bg-blue-700 focus:ring-blue-500'
                    }`}
            >
                {isUploading ? (
                    <> <SpinnerIcon /> <span className="ml-1.5">Uploading...</span> </>
                ) : (
                    <> <UploadIcon /> Re-upload </>
                )}
            </button>
        </div>
    );
};

// Mapping from field path to the 'documentType' key expected by the upload API
const documentPathToTypeMap = {
    commercialRegistration: "commercialRegistration",
    tradeLicense: 'tradeLicense',
    taxCard: 'taxCard',
    establishmentCard: 'establishmentCard',
    memorandumOfAssociation: 'memorandumOfAssociation',
    articleOfAssociation: 'articleOfAssociation',
    commercialCreditReport: 'commercialCreditReport',
    otherDocument: 'otherDocument',
    otherDocumentTwo: 'otherDocumentTwo',
    otherDocument3: 'otherDocument3',
    otherDocument4: 'otherDocument4',
    otherDocument5: 'otherDocument5',
    otherDocument6: 'otherDocument6',
    otherDocument7: 'otherDocument7',
    otherDocument8: 'otherDocument8',
    otherDocument9: 'otherDocument9',
    otherDocument10: 'otherDocument10',
    'kyc.qatariId': "qatariId",
    'kyc.passport': "passport",
    'kyc.utilityBill': "utilityBill",
    bankStatement: "bankStatement", // Assuming root level takes precedence if duplicates exist
    // 'kyc.bankStatement': "bankStatement", // Decide if KYC one needs different type key
    'kyc.employmentDetails.employmentLetter': "employmentLetter",
    'kyc.incomeDetails.proofOfIncome': "proofOfIncome",
    ccrDocument: "ccrDocument",
    cashFlowDocument: "cashFlowDocument",
    cashFlow: 'cashFlow', // Assuming root level takes precedence
    invoiceAdditionalDoc1: "invoiceAdditionalDoc1",
    invoiceAdditionalDoc2: "invoiceAdditionalDoc2",
    auditedFinancialReport: 'auditedFinancialReport',
    // Array paths need special handling during iteration (see function)
};

// Define labels for display
const documentPathToLabelMap = {
    commercialRegistration: "Commercial Registration",
    tradeLicense: 'Trade License',
    taxCard: 'Tax Card',
    establishmentCard: 'Establishment Card',
    memorandumOfAssociation: 'Memorandum of Association',
    articleOfAssociation: 'Article of Association',
    commercialCreditReport: 'Commercial Credit Report',
    otherDocument: 'Other Document 1',
    otherDocumentTwo: 'Other Document 2',
    otherDocument3: 'Other Document 3',
    otherDocument4: 'Other Document 4',
    otherDocument5: 'Other Document 5',
    otherDocument6: 'Other Document 6',
    otherDocument7: 'Other Document 7',
    otherDocument8: 'Other Document 8',
    otherDocument9: 'Other Document 9',
    otherDocument10: 'Other Document 10',
    'kyc.qatariId': "Qatari ID",
    'kyc.passport': "Passport (KYC)",
    'kyc.utilityBill': "Utility Bill",
    bankStatement: "Bank Statement",
    'kyc.bankStatement': "Bank Statement (KYC)",
    'kyc.employmentDetails.employmentLetter': "Employment Letter",
    'kyc.incomeDetails.proofOfIncome': "Proof of Income",
    ccrDocument: "CCR Document",
    cashFlowDocument: "Cash Flow Document",
    cashFlow: 'Cash Flow / Ledger',
    invoiceAdditionalDoc1: "Invoice Additional Doc 1",
    invoiceAdditionalDoc2: "Invoice Additional Doc 2",
    auditedFinancialReport: 'Audited Financial Report',
};


const findRejectedDocuments = (userData) => {
    const rejected = [];
    if (!userData) return rejected;

    // --- Check Root Level & KYC Level Docs ---
    // (Keep existing logic for non-array docs)
    for (const path in documentPathToLabelMap) {
        const docData = getNested(userData, path);
        if (docData && typeof docData === 'object' && docData.verificationStatus === 'REJECTED') {
            const documentType = documentPathToTypeMap[path];
            if (documentType) {
                rejected.push({
                    label: documentPathToLabelMap[path] || path,
                    path: path,
                    notes: docData.verificationNotes || 'No reason provided.',
                    type: documentType,
                    filePath: docData.filePath || '', // Include file path for display
                    // No arrayIndex for these
                });
            }
        }
    }


    // --- Check Arrays ---
    // Shareholders
    (userData.shareholders || []).forEach((sh, index) => { // <-- Capture index here
        const basePath = `shareholders.${index}`;
        ['passport', 'qid', 'proofOfAddress'].forEach(docKey => {
            const fullPath = `${basePath}.${docKey}`;
            const docData = getNested(sh, docKey);
            if (docData && typeof docData === 'object' && docData.verificationStatus === 'REJECTED') {
                // Use a type format that helps identify it's a shareholder doc + the key
                const shareholderDocType = `shareholder_${docKey}`; // e.g., shareholder_passport

                rejected.push({
                    label: `Shareholder ${index + 1}: ${docKey.charAt(0).toUpperCase() + docKey.slice(1)}`,
                    path: fullPath, // Full path needed for status update API
                    notes: docData.verificationNotes || 'No reason provided.',
                    type: shareholderDocType, // Specific type identifier
                    filePath: docData.filePath || '', // Include file path for display
                    arrayIndex: index // <-- **ADD arrayIndex**
                });
            }
        });
    });

    // Directors
    (userData.kyc?.directors || []).forEach((dir, index) => {
        const docKey = 'idDocument';
        const fullPath = `kyc.directors.${index}.${docKey}`;
        const docData = getNested(dir, docKey);
        if (docData && typeof docData === 'object' && docData.verificationStatus === 'REJECTED') {
            // Assuming the backend requires a specific type key like 'directorIdDocument'
            // and potentially the index/ID passed separately if using the generic uploader
            // or a dedicated API like uploadDirectorDocument.
            // For now, follow the shareholder pattern.
            rejected.push({
                label: `Director ${index + 1} (${dir.directorName || 'N/A'}): ID Document`,
                path: fullPath,
                notes: docData.verificationNotes || 'No reason provided.',
                type: 'directorIdDocument', // Base type from map, handling needs clarification
                filePath: docData.filePath || '', // Include file path for display
                arrayIndex: index // Store index
            });
        }
    });


    // Add loops for buyers, signatories, owners similarly if needed...

    return rejected;
};


const fetchLenderDetails = async (lenderId, token) => {
    if (!lenderId || !token) return null;
    try {
        const response = await axios.get(`${config.apiUrl}/ops/invoiceFinancing/lenders/${lenderId}`, {
            headers: { 'x-auth-token': token }
        });
        return response.data;
    } catch (error) {
        console.error(`Error fetching lender details for ID ${lenderId}:`, error);
        return null;
    }
};

const fetchOfferDetails = async (offerId, token) => {
    if (!offerId || !token) return null;
    try {
        // *** Make sure this endpoint exists and returns the offer details including invoiceContract ***
        const response = await axios.get(`${config.apiUrl}/ops/invoiceFinancing/offer/${offerId}`, {
            headers: { 'x-auth-token': token }
        });
        // Check if the response contains the offer directly or nested
        return response.data.offer || response.data; // Adjust if the structure differs
    } catch (error) {
        console.error(`Error fetching offer details for ID ${offerId}:`, error);
        return null;
    }
};


// --- Icons ---
const CheckIcon = ({ className = "w-5 h-5 text-green-500" }) => (
    <svg className={className} fill="currentColor" viewBox="0 0 20 20"><path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" /></svg>
);
const SpinnerIcon = () => ( // Used in KFS Modal Button
    <svg className="animate-spin h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle><path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path></svg>
);

// Helper function to check document verification status
const checkDocumentVerificationStatus = (userData) => {
    if (!userData) return 'PENDING';

    const documentPaths = [
        "commercialRegistration", "tradeLicense", "taxCard", "establishmentCard",
        "memorandumOfAssociation", "articleOfAssociation", "commercialCreditReport",
        "otherDocument",
        "otherDocumentTwo",
        "otherDocument3",
        "otherDocument4",
        "otherDocument5",
        "otherDocument6",
        "otherDocument7",
        "otherDocument8",
        "otherDocument9",
        "otherDocument10",
        "kyc.qatariId",
        "kyc.passport",
        "kyc.utilityBill", "bankStatement", "kyc.employmentDetails.employmentLetter",
        "kyc.incomeDetails.proofOfIncome", "ccrDocument", "cashFlowDocument",
        "cashFlow", "invoiceAdditionalDoc1", "invoiceAdditionalDoc2", "auditedFinancialReport"
    ];

    for (const path of documentPaths) {
        const docData = getNested(userData, path);
        // Only check verification status if document data exists (i.e., uploaded)
        if (docData && docData.filePath && docData.verificationStatus !== 'VERIFIED') {
            return docData.verificationStatus || 'PENDING'; // Return the specific status or PENDING
        }
    }
    return 'VERIFIED';
};

// Helper function to check shareholder KYC status
const checkShareholderKycStatus = (userData) => {
    if (!userData || !userData.shareholders) return 'PENDING';

    for (const shareholder of userData.shareholders) {
        if (shareholder.kycVerificationStatus !== 'APPROVED') {
            return shareholder.kycVerificationStatus || 'PENDING'; // Return the specific status or PENDING
        }
    }
    return 'APPROVED';
};

// Helper function to check MSME KYC status
const checkMsmeKycStatus = (userData) => {
    if (!userData || !userData.kyc) return 'PENDING';
    return userData.kyc.verificationStatus || 'PENDING';
};


const formatStatusText = (status) => {
    if (!status) return 'N/A';
    const formatted = status.replace(/_/g, ' ').toLowerCase();
    return formatted.charAt(0).toUpperCase() + formatted.slice(1);
};

const VerificationStatusSection = ({ userData }) => {
    const kybVerificationStatus = userData?.commercialRegistration?.verificationStatus || 'PENDING';
    const documentVerificationStatus = checkDocumentVerificationStatus(userData);
    const msmeVerificationStatus = checkMsmeKycStatus(userData);
    const shareholders = userData?.shareholders || [];

    // Determine overall status for this section
    let verificationStatusLabel = "Pending";
    let verificationIcon = <ClockIcon className="w-5 h-5 text-yellow-500 mr-2 flex-shrink-0" />;
    let verificationStatusColor = "text-yellow-600";

    const hasRejectedDocs = findRejectedDocuments(userData).length > 0;

    const allApproved = (kybVerificationStatus === 'APPROVED' || kybVerificationStatus === 'VERIFIED') &&
        (documentVerificationStatus === 'VERIFIED') &&
        (msmeVerificationStatus === 'APPROVED' || msmeVerificationStatus === 'VERIFIED') &&
        shareholders.every(sh => sh.kycVerificationStatus === 'APPROVED');

    const hasUnderReview = kybVerificationStatus === 'UNDER_REVIEW' || kybVerificationStatus === 'REVIEW' ||
        documentVerificationStatus === 'UNDER_REVIEW' ||
        msmeVerificationStatus === 'UNDER_REVIEW' || msmeVerificationStatus === 'REVIEW' ||
        shareholders.some(sh => sh.kycVerificationStatus === 'UNDER_REVIEW' || sh.kycVerificationStatus === 'REVIEW');

    if (allApproved) {
        verificationStatusLabel = "Success";
        verificationIcon = <CheckCircleIcon className="w-5 h-5 text-green-500 mr-2 flex-shrink-0" />;
        verificationStatusColor = "text-green-600";
    } else if (hasRejectedDocs || kybVerificationStatus === 'REJECTED' || documentVerificationStatus === 'REJECTED' || msmeVerificationStatus === 'REJECTED' || shareholders.some(sh => sh.kycVerificationStatus === 'REJECTED')) {
        verificationStatusLabel = "Rejected";
        verificationIcon = <ExclamationIcon className="w-5 h-5 text-red-500 mr-2 flex-shrink-0" />;
        verificationStatusColor = "text-red-600";
    } else if (hasUnderReview) {
        verificationStatusLabel = "Under Review";
        verificationIcon = <ClockIcon className="w-5 h-5 text-yellow-500 mr-2 flex-shrink-0" />;
        verificationStatusColor = "text-yellow-600";
    }

    return (
        <div className="space-y-2 text-sm"> {/* Removed relative positioning */}
            {/* Removed the vertical line div entirely */}

            {/* Verification Status Heading */}
            <div className="flex items-center">
                <span className="text-gray-700 font-semibold text-base mr-2">
                    Verification Status:
                </span>
                <span className={`font-medium flex gap-1 ${verificationStatusColor}`}>
                    {verificationStatusLabel} {/* Dynamic label */}
                    {verificationIcon} {/* Dynamic icon */}
                </span>
            </div>

            {/* Individual Statuses */}
            <div className="space-y-2">
                {/* MSME KYB Status */}
                <div className="flex justify-between items-center text-gray-700">
                    <span>MSME KYB</span>
                    <div className="flex items-center">
                        <span className={`font-medium mr-2 ${kybVerificationStatus === 'APPROVED' || kybVerificationStatus === 'VERIFIED' ? 'text-green-600' : kybVerificationStatus === 'REJECTED' ? 'text-red-600' : 'text-yellow-600'}`}>
                            {formatStatusText(kybVerificationStatus)} {/* Formatted status text */}
                        </span>
                        {(kybVerificationStatus === 'APPROVED' || kybVerificationStatus === 'VERIFIED') ? <CheckCircleIcon className="w-5 h-5 text-green-500" /> : kybVerificationStatus === 'REJECTED' ? <ExclamationIcon className="w-5 h-5 text-red-500" /> : <ClockIcon className="w-5 h-5 text-yellow-500" />}
                    </div>
                </div>

                {/* Document Verification Status */}
                <div className="flex justify-between items-center text-gray-700">
                    <span>Document Verification</span>
                    <div className="flex items-center">
                        <span className={`font-medium mr-2 ${documentVerificationStatus === 'VERIFIED' ? 'text-green-600' : documentVerificationStatus === 'REJECTED' ? 'text-red-600' : 'text-yellow-600'}`}>
                            {formatStatusText(documentVerificationStatus)} {/* Formatted status text */}
                        </span>
                        {documentVerificationStatus === 'VERIFIED' ? <CheckCircleIcon className="w-5 h-5 text-green-500" /> : documentVerificationStatus === 'REJECTED' ? <ExclamationIcon className="w-5 h-5 text-red-500" /> : <ClockIcon className="w-5 h-5 text-yellow-500" />}
                    </div>
                </div>

                {/* Individual Shareholder KYC Statuses */}
                {shareholders.length > 0 && (
                    <div className="pt-2 mt-2 border-t border-dashed">
                        <h4 className="text-sm font-bold text-gray-600 mb-1">Shareholder KYC:</h4>
                        {shareholders.map((shareholder, index) => {
                            const status = shareholder.kycVerificationStatus || 'PENDING';
                            let shareholderName = `${shareholder.firstName || ''} ${shareholder.lastName || ''}`.trim();
                            if (!shareholderName && shareholder.shareholderName) {
                                shareholderName = shareholder.shareholderName;
                            }
                            if (!shareholderName) {
                                shareholderName = `Shareholder ${index + 1}`;
                            }

                            let statusText = formatStatusText(status); // Formatted status text
                            let statusColorClass = 'text-yellow-600';
                            let IconComponent = ClockIcon;

                            if (status === 'APPROVED' || status === 'VERIFIED') {
                                statusColorClass = 'text-green-600';
                                IconComponent = CheckCircleIcon;
                            } else if (status === 'REJECTED') {
                                statusColorClass = 'text-red-600';
                                IconComponent = ExclamationIcon;
                            }

                            return (
                                <div key={shareholder._id || index} className="flex justify-between items-center ml-2 py-1">
                                    <span className="text-gray-700">{shareholderName}</span>
                                    <div className={`flex items-center`}>
                                        <span className={`font-medium mr-2 ${statusColorClass}`}>{statusText}</span>
                                        <IconComponent className={`w-5 h-5 ${statusColorClass.replace('-600', '-500')}`} />
                                    </div>
                                </div>
                            );
                        })}
                    </div>
                )}
            </div>
        </div>
    );
};

// Section 2: Credit Assessment
const CreditAssessmentSection = ({ userData, creditLine }) => {
    const kycStatus = userData?.kyc?.verificationStatus;
    const clStatus = creditLine?.creditLineStatus;

    const allKycApproved = (userData?.commercialRegistration?.verificationStatus === 'APPROVED' || userData?.commercialRegistration?.verificationStatus === 'VERIFIED') &&
        checkDocumentVerificationStatus(userData) === 'VERIFIED' &&
        (userData?.kyc?.verificationStatus === 'APPROVED' || userData?.kyc?.verificationStatus === 'VERIFIED') &&
        (userData?.shareholders || []).every(sh => sh.kycVerificationStatus === 'APPROVED');

    let sectionBgClass = "bg-gray-50"; // Default: greyed out
    let sectionBorderClass = "border-gray-200";

    // Logic to determine focus (darker background)
    if (allKycApproved && (clStatus === 'UNDER_REVIEW' || !clStatus || clStatus === 'NOT_FOUND')) {
        sectionBgClass = "bg-white"; // Focused: lighter background
        sectionBorderClass = "border-gray-300";
    } else if (clStatus === 'APPROVED' || clStatus === 'ACTIVE') {
        sectionBgClass = "bg-white"; // Even if approved, keep it focused.
        sectionBorderClass = "border-gray-300";
    }

    return (
        <div className={`rounded-lg shadow p-5 mb-8 flex items-center space-x-2 text-lg font-semibold text-gray-800 ${sectionBgClass} ${sectionBorderClass}`}>
            <ClockIcon className="w-6 h-6 text-gray-700" /> {/* Clock icon */}
            <span>Credit Assessment is under review</span> {/* Fixed text */}
        </div>
    );
};

// Section 3: Credit Offers
const OfferSection = ({
    merchantOffers = [],
    lenderDetails = {},
    creditLine,
    isAccepting,
    onAcceptOfferClick, // Opens KFS Modal
    onViewContractClick, // Opens Contract Modal
    onSignContractClick, // Navigate to contract signing
    formatCurrency,
    loading, // Pass loading state
    userId // *** Pass userId ***
}) => {
    const hasOffers = merchantOffers.length > 0;
    // Determine if an offer is accepted based on the specific criteria check within OfferTable
    const trulyAcceptedOffer = merchantOffers.find(offer =>
        offer.status === 'ACCEPTED' &&
        offer.merchantId === userId &&
        offer.offerType === 'creditLineOffer'
    );
    const isAnyOfferTrulyAccepted = Boolean(trulyAcceptedOffer);

    // Filter offers to only display relevant types (creditLineOffer)
    const creditLineOffers = merchantOffers.filter(offer => offer.offerType === 'creditLineOffer');

    // Check if any offer has a signed contract, is APPROVED, or if the credit line status is APPROVED/ACTIVE
    const hasSignedContract = merchantOffers.some(offer => offer.invoiceContract?.signedUrl || offer.status === 'APPROVED') ||
        creditLine?.creditLineStatus === 'APPROVED' || creditLine?.creditLineStatus === 'ACTIVE';

    let offerStatusMessage = null;
    if (!isAnyOfferTrulyAccepted && creditLineOffers.length > 0) { // Only show message if there are offers but none are accepted
        if (creditLine?.creditLineStatus === 'UNDER_REVIEW') {
            offerStatusMessage = <div className={`bg-[#fff1d2] p-3 flex items-center text-sm`}>
                {InformationCircleIcon && <InformationCircleIcon className="h-4 w-4 mr-2 flex-shrink-0" />}
                <span className="truncate">Application under review, Offers are preliminary.</span>
            </div>
        } else if (!creditLine || creditLine?.creditLineStatus === 'NOT_FOUND') {
            offerStatusMessage = <p className="text-blue-700 bg-blue-50 p-3 rounded-md text-sm mb-4">Please review and accept an offer below.</p>;
        } else if (['APPROVED', 'ACTIVE'].includes(creditLine?.creditLineStatus)) {
            if (hasSignedContract) {
                // Show the disclaimer when a contract is signed
                offerStatusMessage = <p className="text-gray-700 p-3 rounded-md text-sm mb-4" style={{ backgroundColor: '#FFF9E0' }}>* Banks/FIs will make the final offer post verifying the documents and assessing the credit worthiness</p>;
            } else {
                // Show the original message when no contract is signed
                offerStatusMessage = <p className="text-green-700 bg-green-50 p-3 rounded-md text-sm mb-4">Application approved! Please accept an offer.</p>;
            }
        }
    } else if (isAnyOfferTrulyAccepted && creditLineOffers.length > 0) {
        offerStatusMessage = (
            <div className="text-green-700 bg-green-50 p-3 rounded-md text-sm mb-4 flex items-center">
                <CheckCircleIcon className="h-5 w-5 mr-2" /> Offer accepted. View the accepted offer and contract below.
            </div>
        );
    }


    return (
        <div className="bg-white rounded-lg shadow p-5 border border-gray-200 mb-8">
            <h1 className="text-2xl font-bold text-gray-800">Credit Offers</h1>
            {offerStatusMessage}
            {creditLineOffers.length > 0 ? (
                <OfferTable
                    offers={creditLineOffers} // Pass filtered offers
                    lenders={lenderDetails}
                    onAcceptClick={onAcceptOfferClick}
                    onViewContractClick={onViewContractClick} // Pass view contract handler
                    onSignContractClick={onSignContractClick} // Pass contract signing handler
                    formatCurrency={formatCurrency}
                    isAccepting={isAccepting}
                    userId={userId} // *** Pass userId down ***
                    creditLine={creditLine} // Pass credit line for status checks
                />
            ) : (
                <div className="flex flex-col items-center justify-center p-8 bg-gray-100 rounded-md">
                    <img src={SalorTelescope} alt="No offers available" className="w-32 h-auto mb-4 opacity-75" />
                    <p className="text-gray-600 text-center text-sm">
                        {loading ? 'Loading offers...' : 'No credit offers currently available.'}
                    </p>
                </div>
            )}
        </div>
    );
};

// Offer Table Sub-Component - Uses specific acceptance logic
const OfferTable = ({
    offers = [],            // Array of offer objects from API (default to empty)
    lenders = {},           // Map of lender details { lenderId: lenderData } (default to empty)
    onAcceptClick,          // Handler for KFS Modal trigger: (offerId) => void
    onViewContractClick,    // Handler for View Contract Modal trigger: () => void
    onSignContractClick,    // Handler for contract signing navigation: (offerId) => void
    formatCurrency,         // Defined globally
    isAccepting,            // Loading state for KFS confirm action: boolean
    userId,                 // *** Logged-in user's ID from SharedCache ***
    creditLine              // Credit line object for status checks
}) => {

    // 1. Define the function to check if an offer meets the "truly accepted" criteria
    const isOfferTrulyAccepted = (offer, currentUserId) => {
        return (
            offer?.status === 'ACCEPTED' &&          // Status is explicitly 'ACCEPTED'
            offer?.merchantId === currentUserId &&   // Belongs to the current user
            offer?.offerType === 'creditLineOffer'   // Is a credit line offer
        );
    };

    // 2. Find the first offer (if any) that meets these criteria
    const trulyAcceptedOffer = offers.find(offer => isOfferTrulyAccepted(offer, userId));

    // 3. Determine the overall state
    const isAnyOfferTrulyAccepted = Boolean(trulyAcceptedOffer);
    const trulyAcceptedOfferId = trulyAcceptedOffer?._id; // Get the ID if found

    // No longer filter here, assuming 'offers' already contains only creditLineOffers if passed from OfferSection
    // If not, add: const creditLineOffers = offers.filter(offer => offer.offerType === 'creditLineOffer');
    // And use creditLineOffers.map below

    return (
        <div className="mt-4 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {offers.map((offer) => {
                const lender = lenders[offer.lenderId];
                const processingFeeValue = offer.processingFee.value;
                const processingFeeType = offer.processingFee.type;
                const processingFeeDisplay = processingFeeType === 'percentage'
                    ? `${processingFeeValue ?? '?'}%`
                    : formatCurrency(processingFeeValue);

                const isThisTheTrulyAcceptedOffer = isAnyOfferTrulyAccepted && trulyAcceptedOfferId === offer._id;

                return (
                    <div
                        key={offer._id}
                        className={`bg-white rounded-lg shadow-md p-5 border ${isThisTheTrulyAcceptedOffer
                            ? 'border-green-400 ring-1 ring-green-400'
                            : (isAnyOfferTrulyAccepted ? 'border-gray-200 opacity-50' : 'border-gray-200')
                            } transition-all flex flex-col h-full`}
                    >
                        {/* Lender Info */}
                        <div className="flex items-center mb-4">
                            <div className="flex-shrink-0 h-10 w-10">
                                {lender?.logoUrl ? (
                                    <img
                                        className="h-10 w-10 rounded-md object-contain border bg-white"
                                        src={lender.logoUrl}
                                        alt={`${lender.lenderName || ''} Logo`}
                                    />
                                ) : (
                                    <div className="h-10 w-10 bg-gray-200 rounded-md flex items-center justify-center text-gray-500 text-xs">
                                        No Logo
                                    </div>
                                )}
                            </div>
                            <div className="ml-3 overflow-hidden">
                                <div className="text-base font-medium text-gray-900 truncate">
                                    {lender?.lenderName || 'N/A'}
                                </div>
                                <div className="text-xs text-gray-500 truncate">{lender?.lenderType || ''}</div>
                            </div>
                        </div>

                        {/* Offer Details */}
                        <div className="border border-black border-dotted bg-gray-100 p-4 rounded-md mb-4 overflow-hidden">
                            <div className="grid grid-cols-2 gap-4 text-sm">
                                <div>
                                    <p className="text-xs text-gray-500 uppercase tracking-wider mb-0.5">
                                        Credit Limit
                                    </p>
                                    <p className="font-semibold text-gray-800 truncate">
                                        {formatCurrency(offer?.creditLimit)}
                                    </p>
                                </div>
                                <div>
                                    <p className="text-xs text-gray-500 uppercase tracking-wider mb-0.5">
                                        Service Fee %
                                    </p>
                                    <p className="font-semibold text-gray-800 truncate">{offer?.interestRate}%</p>
                                </div>
                                <div>
                                    <p className="text-xs text-gray-500 uppercase tracking-wider mb-0.5">
                                        Tenure
                                    </p>
                                    <p className="font-semibold text-gray-800 truncate">
                                        {offer?.tenureDays} Days
                                    </p>
                                </div>
                                <div>
                                    <p className="text-xs text-gray-500 uppercase tracking-wider mb-0.5">
                                        Processing Fee
                                    </p>
                                    <p className="font-semibold text-gray-800 truncate">
                                        {processingFeeDisplay || 'N/A'}
                                    </p>
                                </div>
                            </div>
                        </div>

                        {/* Action Buttons */}
                        <div className="flex flex-wrap gap-2 mt-auto w-full">
                            {isAnyOfferTrulyAccepted ? (
                                isThisTheTrulyAcceptedOffer ? (
                                    offer.invoiceContract?.signedUrl ||
                                        offer.status === 'APPROVED' ||
                                        creditLine?.creditLineStatus === 'APPROVED' ||
                                        creditLine?.creditLineStatus === 'ACTIVE' ? (
                                        <button
                                            onClick={() => onViewContractClick(offer._id)}
                                            className="ml-4 px-3 py-1.5 bg-blue-600 text-white text-xs font-medium rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition duration-150 ease-in-out flex items-center space-x-1"
                                        >
                                            <EyeIcon className="w-4 h-4" />
                                            <span>View Contract</span>
                                        </button>

                                    ) : (
                                        <>
                                            <button
                                                onClick={() => onViewContractClick(offer._id)}
                                                className="flex-1 min-w-0 inline-flex items-center justify-center gap-2 px-4 py-2 bg-white border border-gray-600 text-gray-800 rounded-md hover:bg-gray-100 transition-colors text-sm font-medium whitespace-nowrap overflow-hidden text-ellipsis"
                                                title="View the accepted contract details"
                                            >
                                                <EyeIcon className="w-4 h-4" />
                                                View Contract
                                            </button>

                                            <button
                                                onClick={() => onSignContractClick(offer._id)}
                                                className="flex-1 min-w-0 inline-flex items-center justify-center gap-2 px-4 py-2 bg-[#004141] text-white rounded-md hover:bg-[#003030] transition-colors text-sm font-medium whitespace-nowrap overflow-hidden text-ellipsis"
                                                title="Sign the contract"
                                            >
                                                Sign Contract
                                            </button>
                                        </>
                                    )
                                ) : (
                                    <span
                                        className="w-full inline-flex items-center justify-center px-4 py-2 text-gray-400 text-sm font-medium cursor-not-allowed border border-gray-200 rounded-md bg-gray-50 whitespace-nowrap overflow-hidden text-ellipsis"
                                        title="An offer has already been accepted"
                                    >
                                        Another offer accepted
                                    </span>
                                )
                            ) : (
                                <>
                                    <button
                                        onClick={() => onViewContractClick(offer._id)}
                                        className="flex-1 min-w-0 inline-flex items-center justify-center px-4 py-2 bg-white border border-gray-600 text-gray-800 rounded-md hover:bg-gray-100 transition-colors text-sm font-medium whitespace-nowrap overflow-hidden text-ellipsis"
                                        title="View the contract details before accepting"
                                    >
                                        <EyeIcon className="w-4 h-4 mr-1" />
                                        View Contract
                                    </button>
                                    <button
                                        onClick={() => onAcceptClick(offer._id)}
                                        className={`flex-1 min-w-0 inline-flex items-center justify-center px-4 py-2 rounded-md transition-colors text-sm font-medium whitespace-nowrap overflow-hidden text-ellipsis ${isAccepting
                                            ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                                            : 'bg-green-600 hover:bg-green-700 text-white'
                                            }`}
                                        disabled={isAccepting}
                                    >
                                        {isAccepting ? 'Processing...' : 'Accept Offer'}
                                    </button>
                                </>
                            )}
                        </div>
                    </div>
                );
            })}
        </div>
    );


};

// Section 4: Credit Line Details
const CreditLineDetailsSection = ({ creditLine, lenderDetails = {}, formatCurrency, onViewContractClick }) => {
    // Show details if CL exists AND its offerAccepted flag is true AND status is suitable
    const showDetails = creditLine?.offerAccepted === true && ['ACTIVE', 'APPROVED', 'SUSPENDED', 'EXPIRED'].includes(creditLine?.creditLineStatus);
    const lender = (creditLine?.lenderId && lenderDetails) ? lenderDetails[creditLine.lenderId] : null;
    // We'll use the acceptedOfferId to view the contract

    return (
        <div className="bg-white rounded-lg shadow p-5 border border-gray-200">
            <div className="flex justify-between items-center mb-3 border-b pb-2">
                <h3 className="text-lg font-semibold text-gray-800">4. Credit Line Details</h3>
                {/* Show View Contract button if details are shown AND we have a URL */}
                {showDetails && creditLine?.acceptedOfferId && (
                    <button
                        onClick={() => onViewContractClick(creditLine.acceptedOfferId)} // Pass the accepted offer ID
                        className="ml-4 px-3 py-1.5 bg-blue-600 text-white text-xs font-medium rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition duration-150 ease-in-out"
                    >
                        <EyeIcon className="w-4 h-4 mr-1" />
                        View Contract
                    </button>
                )}
            </div>

            {showDetails && creditLine && formatCurrency ? (
                <div className="mt-4">
                    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-x-6 gap-y-5 text-sm">
                        {/* Map data from creditLine object */}
                        <div><p className="text-xs text-gray-500 uppercase tracking-wider mb-0.5">Credit Limit</p><p className="font-semibold text-gray-800 text-base">{formatCurrency(creditLine.creditLimit)}</p></div>
                        <div><p className="text-xs text-gray-500 uppercase tracking-wider mb-0.5">Available Balance</p><p className="font-semibold text-gray-800 text-base">{formatCurrency(creditLine.availableBalance)}</p></div>
                        <div><p className="text-xs text-gray-500 uppercase tracking-wider mb-0.5">Utilized Amount</p><p className="font-semibold text-gray-800 text-base">{formatCurrency(creditLine.utilizedAmount)}</p></div>
                        <div><p className="text-xs text-gray-500 uppercase tracking-wider mb-0.5">Service Fee %</p><p className="font-semibold text-gray-800 text-base">{creditLine.interestRate}%</p></div>
                        <div><p className="text-xs text-gray-500 uppercase tracking-wider mb-0.5">Tenure</p><p className="font-semibold text-gray-800 text-base">{creditLine.tenure} days</p></div>
                        <div><p className="text-xs text-gray-500 uppercase tracking-wider mb-0.5">Processing Fee</p><p className="font-semibold text-gray-800 text-base">{creditLine.processingFeeType === 'percentage' ? `${creditLine.processingFee}%` : formatCurrency(creditLine.processingFee)}</p></div>
                        {/* <div><p className="text-xs text-gray-500 uppercase tracking-wider mb-0.5">Commitment Fee</p><p className="font-semibold text-gray-800 text-base">{creditLine.commitmentFeePercentage}%</p></div> */}
                        {/* <div><p className="text-xs text-gray-500 uppercase tracking-wider mb-0.5">Late Payment Fee</p><p className="font-semibold text-gray-800 text-base">{creditLine.latePaymentFeePercentage}%</p></div> */}
                        <div>
                            <p className="text-xs text-gray-500 uppercase tracking-wider mb-0.5">Status</p>
                            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${creditLine.creditLineStatus === 'ACTIVE' ? 'bg-green-100 text-green-800' :
                                creditLine.creditLineStatus === 'APPROVED' ? 'bg-blue-100 text-blue-800' :
                                    creditLine.creditLineStatus === 'SUSPENDED' ? 'bg-yellow-100 text-yellow-800' :
                                        creditLine.creditLineStatus === 'EXPIRED' ? 'bg-gray-100 text-gray-800' :
                                            'bg-red-100 text-red-800' // REJECTED etc.
                                }`}>{creditLine.creditLineStatus?.replace(/_/g, ' ') || 'N/A'}</span>
                        </div>
                        <div className="sm:col-span-2 lg:col-span-1 flex items-center pt-2">
                            {lender ? (
                                <><div className="flex-shrink-0 h-10 w-10 mr-3">{lender.logoUrl ? <img className="h-10 w-10 rounded-md object-contain border bg-white" src={lender.logoUrl} alt={`${lender.lenderName} Logo`} /> : <div className="h-10 w-10 bg-gray-200 rounded-md flex items-center justify-center text-gray-500 text-xs font-medium">No Logo</div>}</div><div><p className="text-xs text-gray-500 uppercase tracking-wider mb-0.5">Lender</p><div className="text-sm font-semibold text-gray-900">{lender.lenderName}</div></div></>
                            ) : (<div> <p className="text-xs text-gray-500 uppercase tracking-wider mb-0.5">Lender</p><p className="text-gray-500 italic text-sm">Details unavailable</p></div>)}
                        </div>
                    </div>
                </div>
            ) : (
                <p className="text-gray-500 italic mt-4 text-sm">
                    {creditLine?.offerAccepted ? 'Loading credit line details...' : 'Credit line details will appear here once an offer is accepted and finalized.'}
                    {creditLine && !showDetails && ` (Current Status: ${creditLine.creditLineStatus?.replace(/_/g, ' ') || 'Unknown'})`}
                </p>
            )}
        </div>
    );
};


// --- Loading & Error Components ---

const LoadingIndicator = ({ fileName }) => {
    return (
        <LoadingModal
            message={fileName ? `Loading ${fileName}...` : "Please wait a moment while we load your journey data"}
        />
    );
};

const ErrorMessage = ({ message, onClose }) => (
    <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded-md mb-4 relative"><strong className="font-bold">Error: </strong><span className="block sm:inline">{message || 'An error occurred.'}</span>{onClose && (<button onClick={onClose} className="absolute top-0 bottom-0 right-0 px-4 py-3 text-red-500 hover:text-red-700"><svg className="fill-current h-6 w-6" role="button" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20"><title>Close</title><path d="M14.348 14.849a1.2 1.2 0 0 1-1.697 0L10 11.819l-2.651 3.03a1.2 1.2 0 1 1-1.697-1.697l3.03-2.651-3.03-2.651a1.2 1.2 0 1 1 1.697-1.697L10 8.183l2.651-3.031a1.2 1.2 0 1 1 1.697 1.697l-3.03 2.651 3.03 2.651a1.2 1.2 0 0 1 0 1.697z" /></svg></button>)}</div>
);

// --- Main Component (ActivationJourney) ---
const ActivationJourney = () => {
    // --- State ---
    const [creditLine, setCreditLine] = useState(null);
    const [userData, setUserData] = useState(null);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const [merchantOffers, setMerchantOffers] = useState([]);
    const [lenderDetails, setLenderDetails] = useState({});
    const [acceptedOfferData, setAcceptedOfferData] = useState(null); // Stores full details of accepted offer
    const [isAccepting, setIsAccepting] = useState(false); // Loading state for KFS modal confirmation only
    const [isKfsModalOpen, setIsKfsModalOpen] = useState(false);
    const [kfsOfferData, setKfsOfferData] = useState(null);
    const [isContractModalOpen, setIsContractModalOpen] = useState(false); // State for contract modal
    const [rejectedDocs, setRejectedDocs] = useState([]); // <-- Add state for rejected docs
    const [documentVerificationStatus, setDocumentVerificationStatus] = useState('PENDING'); // <--- REMOVE THIS LINE
    const [shareholderVerificationStatus, setShareholderVerificationStatus] = useState('PENDING'); // <--- REMOVE THIS LINE
    const [msmeVerificationStatus, setMsmeVerificationStatus] = useState('PENDING'); // <--- REMOVE THIS LINE
    const [overallVerificationStatus, setOverallVerificationStatus] = useState('PENDING'); // <--- REMOVE THIS LINE

    // --- Hooks & Context ---
    const history = useHistory();
    const user = SharedCache.get("user") || {};
    const userId = user._id || user.id || ""; // Get userId from cache
    const token = SharedCache.get("token") || "placeholdertoken";

    console.log("token", token);

    // Contract signing navigation handler
    const handleContractSigningNavigation = (offerId) => {
        // Navigate using the OFFER ID, as it represents the specific financial arrangement
        console.log("Navigating to contract signing for offerId:", offerId);
        // Navigate to the contract signing page with the offer ID
        history.push(`/creditLineContract/${offerId}`);
    };

    useEffect(() => {
        if (userData) {
            console.log("User data updated, finding rejected documents...");
            const rejected = findRejectedDocuments(userData);
            console.log("Rejected documents found:", rejected);
            setRejectedDocs(rejected);
        } else {
            setRejectedDocs([]); // Clear if no user data
        }
    }, [userData]);

    const handleReuploadSuccess = (documentPath) => {
        console.log(`Removing ${documentPath} from rejected list.`);
        setRejectedDocs(prevDocs => prevDocs.filter(doc => doc.path !== documentPath));
        // Optional: Refetch all data? For now, just remove visually.
        // fetchAllData(); // Could cause flicker
    };

    // --- Data Fetching Effect ---
    useEffect(() => {
        const fetchAllData = async () => {
            if (!userId || !token) { setError("User session not found."); setLoading(false); return; }
            setLoading(true); setError(null);
            setCreditLine(null); setUserData(null); setMerchantOffers([]);
            setLenderDetails({}); setAcceptedOfferData(null);

            let fetchedCreditLine = null;
            let fetchedUser = null;
            let fetchedOffers = [];
            let fetchedLenderDetailsMap = {};
            let apiAcceptedOfferId = null;

            try {
                console.log("Fetching User and Credit Line data...");
                const [creditLineSettled, userSettled] = await Promise.allSettled([
                    axios.get(`${config.apiUrl}/ops/invoiceFinancing/fetchCreditLine/${userId}`, { headers: { 'x-auth-token': token } }),
                    axios.get(`${config.apiUrl}/ops/invoiceFinancing/users/id/${userId}`, { headers: { 'x-auth-token': token } })
                ]);

                if (userSettled.status === 'fulfilled') {
                    fetchedUser = userSettled.value.data;
                    setUserData(fetchedUser); console.log("User data fetched");

                    // Calculate and set verification statuses here, AFTER setUserData
                    const docStatus = checkDocumentVerificationStatus(fetchedUser);
                    setDocumentVerificationStatus(docStatus);

                    const shStatus = checkShareholderKycStatus(fetchedUser);
                    setShareholderVerificationStatus(shStatus);

                    const msmeStatus = checkMsmeKycStatus(fetchedUser);
                    setMsmeVerificationStatus(msmeStatus);

                    // Implement your logic to determine the overall verification status
                    // based on documentVerificationStatus, shareholderVerificationStatus, and msmeVerificationStatus
                    // Example:
                    let overallStatus = 'PENDING';
                    if (docStatus === 'VERIFIED' && shStatus === 'APPROVED' && msmeStatus === 'APPROVED') {
                        overallStatus = 'VERIFIED';
                    } else if (docStatus === 'REJECTED' || shStatus === 'REJECTED' || msmeStatus === 'REJECTED') {
                        overallStatus = 'REJECTED';
                    }
                    setOverallVerificationStatus(overallStatus);

                } else { console.error("Error fetching user data:", userSettled.reason); }

                if (creditLineSettled.status === 'fulfilled') {
                    fetchedCreditLine = creditLineSettled.value.data;
                    setCreditLine(fetchedCreditLine); console.log("Credit Line fetched:", fetchedCreditLine?.creditLineStatus);
                    // Get accepted offer ID from the credit line itself if accepted
                    apiAcceptedOfferId = fetchedCreditLine?.offerAccepted ? fetchedCreditLine.acceptedOfferId : null;
                } else {
                    if (creditLineSettled.reason?.response?.status === 404) {
                        console.log("No credit line found (404).");
                        setCreditLine({ creditLineStatus: 'NOT_FOUND', offerAccepted: false });
                    } else { console.error("Error fetching credit line:", creditLineSettled.reason); }
                }

                // Fetch Offers regardless of CL status, as they might be needed for display (disabled/view contract)
                console.log("Fetching merchant offers...");
                try {
                    const offersResponse = await axios.get(`${config.apiUrl}/ops/invoiceFinancing/offers/${userId}`, { headers: { 'x-auth-token': token } });
                    if (offersResponse.data?.offers?.length > 0) {
                        fetchedOffers = offersResponse.data.offers; // Keep all offers for now
                        setMerchantOffers(fetchedOffers);
                        console.log(`Workspaceed ${fetchedOffers.length} total offers (incl. non-creditLine)`);

                        const uniqueOfferLenderIds = [...new Set(
                            fetchedOffers.map(offer => offer.lenderId).filter(id => id)
                        )];
                        if (uniqueOfferLenderIds.length > 0) {
                            console.log("Fetching details for offer lenders:", uniqueOfferLenderIds);
                            const lenderPromises = uniqueOfferLenderIds.map(id => fetchLenderDetails(id, token));
                            const results = await Promise.allSettled(lenderPromises);
                            results.forEach((result, index) => {
                                if (result.status === 'fulfilled' && result.value) {
                                    fetchedLenderDetailsMap[uniqueOfferLenderIds[index]] = result.value;
                                }
                            });
                            console.log("Offer lender details fetched.");
                        }
                    } else { console.log("No offers found for this user."); }
                } catch (offersErr) { console.error("Error fetching merchant offers:", offersErr); }


                // Fetch Accepted Offer Details if ID was found in the credit line
                if (apiAcceptedOfferId) {
                    console.log(`Workspaceing details for accepted offer ID: ${apiAcceptedOfferId}`);
                    const offerData = await fetchOfferDetails(apiAcceptedOfferId, token);
                    if (offerData) {
                        setAcceptedOfferData(offerData); console.log("Accepted offer details fetched.");
                        // Ensure lender details for this accepted offer are fetched if missing
                        if (offerData.lenderId && !fetchedLenderDetailsMap[offerData.lenderId]) {
                            console.log(`Workspaceing lender details for accepted offer's lender: ${offerData.lenderId}`);
                            const lenderData = await fetchLenderDetails(offerData.lenderId, token);
                            if (lenderData) { fetchedLenderDetailsMap[offerData.lenderId] = lenderData; }
                        }
                    } else {
                        console.warn(`Could not fetch details for accepted offer ID: ${apiAcceptedOfferId}`);
                    }
                }

                // Fetch Lender Details for the Credit Line's lender (if different/not yet fetched)
                const clLenderId = fetchedCreditLine?.lenderId;
                if (clLenderId && !fetchedLenderDetailsMap[clLenderId]) {
                    console.log(`Workspaceing lender details for CL lender: ${clLenderId}`);
                    const lenderData = await fetchLenderDetails(clLenderId, token);
                    if (lenderData) { fetchedLenderDetailsMap[clLenderId] = lenderData; }
                }

                setLenderDetails(fetchedLenderDetailsMap); // Set final map

            } catch (err) {
                console.error("Unexpected error during data fetch:", err);
                setError("An error occurred while loading application status.");
            } finally {
                setLoading(false);
            }
        };
        fetchAllData();
    }, [userId, token]); // Dependency array

    // --- Modal Handlers ---
    const handleOpenKfsModal = (offerId) => {
        // Filter only creditLineOffers before finding the one to show in KFS
        const offer = merchantOffers.find(o => o._id === offerId && o.offerType === 'creditLineOffer');
        const lender = lenderDetails[offer?.lenderId];
        if (offer && lender) {
            setKfsOfferData({ ...offer, lender });
            setIsKfsModalOpen(true);
            fetchLenderDetails(offer.lenderId, token);
        }
        else { setError("Could not load offer details. Please refresh."); console.error("Failed to find offer/lender for KFS:", { offerId, offer, lender, lenderDetails }); }
    };
    const handleCloseKfsModal = () => { setIsKfsModalOpen(false); setKfsOfferData(null); };
    const handleOpenContractModal = (offerId) => {
        // If an offerId is provided, we're viewing a contract for a specific offer
        if (offerId) {
            // Store the selected offer ID for the modal
            setSelectedOfferId(offerId);
            setIsContractModalOpen(true);
        } else {
            // If no offerId is provided, use the acceptedOfferId from the credit line
            setSelectedOfferId(creditLine?.acceptedOfferId || null);
            setIsContractModalOpen(true);
        }

        // The actual contract URL loading is now handled in the modal component
        // with the useEffect hook that calls getContractUrl
    };
    const handleCloseContractModal = () => {
        setIsContractModalOpen(false);
        setSelectedOfferId(null);
    };

    // --- Action Handlers ---
    const handleModalConfirmAccept = async () => { // Confirms KFS and Accepts Offer
        if (!kfsOfferData?._id) return;
        const offerIdToAccept = kfsOfferData._id;
        setIsAccepting(true); handleCloseKfsModal();
        console.log(`Accepting offer via KFS: ${offerIdToAccept}`);
        try {
            await axios.post(`${config.apiUrl}/ops/invoiceFinancing/offers/${offerIdToAccept}/accept`, {}, { headers: { 'x-auth-token': token } });
            console.log(`Offer ${offerIdToAccept} accepted via API.`);

            // --- Immediately update the merchantOffers state to reflect acceptance ---
            setMerchantOffers(prevOffers => {
                return prevOffers.map(offer => {
                    if (offer._id === offerIdToAccept) {
                        return { ...offer, status: 'ACCEPTED' };
                    }
                    return offer;
                });
            });

            // --- Refresh Data for Accuracy ---
            setLoading(true); // Show loading indicator while refreshing
            const [clRes, offerRes, offersRes] = await Promise.allSettled([
                axios.get(`${config.apiUrl}/ops/invoiceFinancing/fetchCreditLine/${userId}`, { headers: { 'x-auth-token': token } }),
                fetchOfferDetails(offerIdToAccept, token), // Fetch accepted offer details again
                axios.get(`${config.apiUrl}/ops/invoiceFinancing/offers/${userId}`, { headers: { 'x-auth-token': token } }) // Fetch all updated offers
            ]);

            if (clRes.status === 'fulfilled') {
                setCreditLine(clRes.value.data);
                console.log("Re-fetched credit line after acceptance.");
            } else {
                console.error("Failed to re-fetch credit line after acceptance:", clRes.reason);
                // Keep existing CL data? Or set error? Let's keep it for now.
            }

            if (offerRes.status === 'fulfilled' && offerRes.value) {
                setAcceptedOfferData(offerRes.value);
                console.log("Re-fetched accepted offer details.");
                // Ensure lender details are available
                if (offerRes.value.lenderId && !lenderDetails[offerRes.value.lenderId]) {
                    const lenderData = await fetchLenderDetails(offerRes.value.lenderId, token);
                    if (lenderData) {
                        setLenderDetails(prev => ({ ...prev, [offerRes.value.lenderId]: lenderData }));
                    }
                }
            } else {
                console.error("Failed to re-fetch accepted offer details:", offerRes.reason);
                setAcceptedOfferData(null); // Clear potentially stale data
            }

            // Update merchant offers with fresh data from API
            if (offersRes.status === 'fulfilled' && offersRes.value?.data?.offers) {
                setMerchantOffers(offersRes.value.data.offers);
                console.log("Re-fetched all merchant offers after acceptance.");
            }

        } catch (acceptError) {
            console.error("Error accepting offer:", acceptError);
            setError(`Failed to accept offer: ${acceptError?.response?.data?.message || acceptError.message}.`);
        } finally {
            setIsAccepting(false); // Stop KFS button loading
            setLoading(false); // Stop page loading indicator
        }
    };

    // State to track the currently selected offer for the contract modal
    const [selectedOfferId, setSelectedOfferId] = useState(null);

    // State to store dynamically generated contract URLs
    const [contractUrls, setContractUrls] = useState({});

    // Get the contract URL for the selected offer
    const getContractUrl = async (offerId) => {
        if (!offerId) return null;

        // If we already have a generated URL for this offer, return it
        if (contractUrls[offerId]) {
            return contractUrls[offerId];
        }

        // Find the offer in merchantOffers
        const offer = merchantOffers.find(o => o._id === offerId);
        if (offer?.invoiceContract?.signedUrl) {
            return offer.invoiceContract.signedUrl;
        }

        // If this is the accepted offer, check acceptedOfferData
        if (acceptedOfferData && acceptedOfferData._id === offerId) {
            if (acceptedOfferData.invoiceContract?.signedUrl) {
                return acceptedOfferData.invoiceContract.signedUrl;
            }
        }

        try {
            // Generate the PDF dynamically using the imported function
            const agreementFlags = {
                agreedAccurate: true,
                readTerms: true,
                readFinancialConsent: true
            };

            // Get the offer details
            const offerDetails = offer || acceptedOfferData || null;

            if (offerDetails) {
                const result = await generateRevolvingCreditPdf(
                    offerDetails,
                    userData,
                    lenderDetails[offerDetails.lenderId] || {},
                    token,
                    config,
                    agreementFlags
                );

                if (result.success && result.signedUrl) {
                    // Store the URL for future use
                    setContractUrls(prev => ({
                        ...prev,
                        [offerId]: result.signedUrl
                    }));
                    return result.signedUrl;
                }
            }
        } catch (error) {
            console.error("Error generating contract PDF:", error);
        }

        // Default to the sample contract if generation fails
        return "/creditLineContract.pdf";
    };

    // --- Render Logic ---
    if (loading) {
        return <LoadingIndicator message="Loading Application Status..." />;
    }
    const shareholders = userData?.shareholders || [];

    const kybVerificationStatus = getNested(userData, 'commercialRegistration.verificationStatus', 'PENDING');
    const allShareholdersApproved = shareholders.every(sh => sh.kycVerificationStatus === 'APPROVED' || sh.kycVerificationStatus === 'VERIFIED');

    const isVerificationJourneyCompleteAndSuccessful =
        (kybVerificationStatus === 'APPROVED' || kybVerificationStatus === 'VERIFIED') &&
        documentVerificationStatus === 'VERIFIED' &&
        (msmeVerificationStatus === 'APPROVED' || msmeVerificationStatus === 'VERIFIED') &&
        allShareholdersApproved;

    let currentFocusedStep = 'verification'; // Default
    if (isVerificationJourneyCompleteAndSuccessful && rejectedDocs.length === 0) {
        currentFocusedStep = 'creditAssessment';
    } else {
        currentFocusedStep = 'verification'; // Remains verification if not complete or has rejections
    }

    return (
        <div className="min-h-screen">
            {error && <ErrorMessage message={error} onClose={() => setError(null)} />}

            {/* Section 1: Activation Journey */}
            <div className="bg-white rounded-lg shadow p-5 border border-gray-200 mb-8">
                <h1 className="text-2xl font-bold mb-6 text-gray-800">Activation Journey</h1>

                <div className="space-y-0"> {/* Container for the three main sections */}

                    {/* Onboarding Journey Section - Always Complete and Static in this view */}
                    <div className="relative">
                        {/* Vertical line connecting to Verification Journey */}
                        <div className="absolute left-[1.125rem] top-8 bottom-0 w-px bg-gray-300"></div>
                        <div className="flex items-center space-x-4 py-4">
                            <div className="w-9 h-9 bg-[#effbef] rounded-full flex items-center justify-center flex-shrink-0 relative z-10">
                                <UserCircleIcon className="w-9 h-9 text-[#45ca77]" />
                            </div>
                            <span className="text-gray-600 font-bold text-base">Onboarding Journey</span>
                            <span className="text-green-600 font-medium text-base">Complete</span>
                            <CheckIcon className="w-6 h-6 text-[#45ca77] ml-1" />
                        </div>
                    </div>

                    {/* Verification Journey Section */}
                    <div className="relative">
                        {/* Vertical line connecting to Credit Assessment Journey */}
                        <div className="absolute left-[1.125rem] top-0 bottom-0 w-px bg-gray-300"></div>

                        {(isVerificationJourneyCompleteAndSuccessful && rejectedDocs.length === 0) ? (
                            // STATE 1: Verification is fully approved and no rejections - show static "Success"
                            <div className="flex items-center space-x-4 py-4">
                                <div className="w-9 h-9 bg-[#effbef] rounded-full flex items-center justify-center flex-shrink-0 relative z-10">
                                    <UserGroupIcon className="w-6 h-6 text-[#45ca77]" /> {/* Green icon */}
                                </div>
                                <span className="text-gray-600 font-bold text-base">Verification Journey</span>
                                <span className="text-green-600 font-medium text-base">Success</span> {/* "Success" status */}
                                <CheckIcon className="w-6 h-6 text-[#45ca77] ml-1" />
                            </div>
                        ) : (
                            // STATE 2: Verification is not fully approved OR has rejections - show detailed status
                            <div className="flex items-start space-x-4 py-4">
                                <div className={`w-9 h-9 ${currentFocusedStep === 'verification' ? 'bg-gray-100' : 'bg-gray-200'} rounded-full flex items-center justify-center flex-shrink-0 relative z-10 mt-0.5`}>
                                    <UserGroupIcon className={`w-6 h-6`} />
                                </div>
                                {/* Content Wrapper for VerificationStatusSection - styled based on focus */}
                                <div className={`flex-1 ${currentFocusedStep === 'verification' ? 'bg-gray-100 border border-gray-200 shadow-sm' : 'bg-white'} rounded-lg px-4 py-3`}>
                                    <VerificationStatusSection userData={userData} />
                                </div>
                            </div>
                        )}
                    </div>

                    {/* Credit Assessment Section */}
                    <div className="relative">
                        {/* No vertical line continuing down from here within this specific timeline block */}
                        <div className="flex items-start space-x-4 py-4">
                            <div className={`w-9 h-9 ${currentFocusedStep === 'creditAssessment' ? 'bg-gray-200' : 'bg-gray-200'} rounded-full flex items-center justify-center flex-shrink-0 relative z-10 mt-0.5`}>
                                {currentFocusedStep === 'creditAssessment' ? (
                                    <ExclamationCircleIcon className="w-6 h-6 text-gray-600" />
                                ) : (
                                    <ExclamationCircleIcon className="w-6 h-6 text-gray-600" />
                                )}
                            </div>
                            {/* Content Wrapper for Credit Assessment step - styled based on focus */}
                            <div className={`flex-1 ${currentFocusedStep === 'creditAssessment' ? 'bg-gray-200 border border-gray-200 shadow-sm' : 'bg-white'} rounded-lg px-2 py-2`}>
                                <div className="flex items-center gap-2">
                                    {/* Replace this with your actual icon or logo component */}
                                    <span className={`text-base ${currentFocusedStep === 'creditAssessment' ? 'text-gray-800 font-semibold' : 'text-gray-600'} font-bold`}>
                                        Credit Assessment is under review
                                    </span>
                                    <ClockIcon className="h-5 w-5" />
                                </div>
                            </div>

                        </div>
                    </div>

                    {/* Rejected Documents Section - Shown if any documents are rejected */}
                    {/* This section is an addendum to the Verification step, not a new timeline step */}
                    {rejectedDocs.length > 0 && (
                        <div className="mt-4 pt-4 border-t border-dashed border-gray-300">
                            <h4 className="text-sm font-semibold text-red-700 mb-3 flex items-center">
                                <ExclamationIcon className="w-5 h-5 text-red-500 mr-1.5" />
                                Action Required: Re-upload Documents
                            </h4>
                            <div className="space-y-3">
                                {rejectedDocs.map(doc => (
                                    <RejectedDocumentItem
                                        key={doc.path}
                                        docInfo={doc}
                                        userId={userId}
                                        token={token}
                                        onReuploadSuccess={handleReuploadSuccess}
                                    />
                                ))}
                            </div>
                        </div>
                    )}
                </div>
            </div>
            {/* Section 2: Credit Offers */}
            <div className="mb-8">
                <OfferSection
                    merchantOffers={merchantOffers}
                    lenderDetails={lenderDetails}
                    creditLine={creditLine}
                    isAccepting={isAccepting}
                    onAcceptOfferClick={handleOpenKfsModal}
                    onViewContractClick={handleOpenContractModal}
                    onSignContractClick={handleContractSigningNavigation}
                    formatCurrency={formatCurrency}
                    loading={loading}
                    userId={userId}
                />

                {/* Credit Line Details Section - ONLY show if creditLineStatus is 'ACTIVE' */}
                {creditLine?.creditLineStatus === 'ACTIVE' && (
                    <CreditLineDetailsSection
                        creditLine={creditLine}
                        lenderDetails={lenderDetails}
                        formatCurrency={formatCurrency}
                        onViewContractClick={handleOpenContractModal}
                    />
                )}
            </div>

            {/* KFS Modal */}
            {isKfsModalOpen && kfsOfferData && (
                <KfsModal
                    isOpen={isKfsModalOpen}
                    onClose={handleCloseKfsModal}
                    offerData={kfsOfferData}
                    onConfirm={handleModalConfirmAccept}
                    isAccepting={isAccepting}
                    formatCurrency={formatCurrency}
                />
            )}

            {/* Contract Modal */}
            {isContractModalOpen && (
                <div
                    className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-60 backdrop-blur-sm"
                    onClick={handleCloseContractModal}
                    role="dialog"
                    aria-modal="true"
                    aria-labelledby="contractModalTitle"
                >
                    <div
                        className="bg-white rounded-lg shadow-xl p-0 max-w-4xl w-full mx-4 my-8 overflow-hidden flex flex-col"
                        onClick={(e) => e.stopPropagation()}
                    >
                        <div className="flex justify-between items-center px-6 py-4 border-b border-gray-200">
                            <h2 id="contractModalTitle" className="text-xl font-semibold text-gray-800">
                                Credit Line Contract
                            </h2>
                            <button
                                onClick={handleCloseContractModal}
                                className="text-gray-400 hover:text-gray-700 text-3xl leading-none font-semibold focus:outline-none"
                                aria-label="Close modal"
                            >
                                &times;
                            </button>
                        </div>

                        <div className="p-1 flex-grow" style={{ height: '75vh' }}>
                            <ContractViewer
                                selectedOfferId={selectedOfferId}
                                getContractUrl={getContractUrl}
                            />
                        </div>
                    </div>
                </div>
            )}
        </div>
    );
};


// Contract Viewer Component - Separate component to properly use React hooks
const ContractViewer = ({ selectedOfferId, getContractUrl }) => {
    const [contractUrl, setContractUrl] = useState("");
    const [isLoading, setIsLoading] = useState(true);

    // Effect to load the contract URL when the modal opens or selectedOfferId changes
    useEffect(() => {
        let isMounted = true;
        setIsLoading(true);

        const loadContractUrl = async () => {
            try {
                if (selectedOfferId) {
                    const url = await getContractUrl(selectedOfferId);
                    if (isMounted) {
                        setContractUrl(url || "/creditLineContract.pdf");
                    }
                }
            } catch (error) {
                console.error("Error loading contract URL:", error);
            } finally {
                if (isMounted) {
                    setIsLoading(false);
                }
            }
        };

        loadContractUrl();

        return () => {
            isMounted = false;
        };
    }, [selectedOfferId, getContractUrl]);
    return (
        <>
            {isLoading && (
                <div className="absolute inset-0 flex items-center justify-center bg-white bg-opacity-80 z-10">
                    <div className="text-center">
                        <div className="w-12 h-12 border-4 border-[#208039] border-t-transparent rounded-full animate-spin mx-auto"></div>
                        <p className="mt-4 text-md text-gray-600">Loading contract...</p>
                    </div>
                </div>
            )}
            <iframe
                src={contractUrl}
                title="Credit Line Contract PDF"
                className="w-full h-full border-0" // Use h-full to fill parent div
            >
                {/* Fallback content if iframe fails or PDF isn't supported */}
                <div className="p-4 text-center text-gray-600">
                    <p>Your browser may not support viewing PDFs directly in this window.</p>
                    <p className="mt-2">You can <a
                        href={contractUrl}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-blue-600 hover:underline font-medium"
                    >download the contract here</a> instead.</p>
                </div>
            </iframe>
        </>
    );
};

// --- KFS Modal Component --- (No Changes Needed)
const KfsModal = ({ isOpen, onClose, offerData, onConfirm, isAccepting, formatCurrency }) => {
    if (!isOpen || !offerData) return null;
    return (<div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-60 backdrop-blur-sm" onClick={onClose} role="dialog" aria-modal="true" aria-labelledby="kfsModalTitle"><div className="bg-white rounded-lg shadow-xl p-0 max-w-lg w-full mx-4 my-8 overflow-hidden flex flex-col" onClick={(e) => e.stopPropagation()}><div className="flex justify-between items-center px-6 py-4 border-b border-gray-200"><h2 id="kfsModalTitle" className="text-xl font-semibold text-gray-800">Key Fact Statement</h2><button onClick={onClose} disabled={isAccepting} className="text-gray-400 hover:text-gray-700 text-3xl leading-none font-semibold focus:outline-none" aria-label="Close modal">&times;</button></div><div className="px-6 py-5 space-y-5"><div className="flex items-center space-x-4 pb-4 border-b">{offerData.lender?.logoUrl ? (<img className="h-12 w-12 rounded-md object-contain flex-shrink-0" src={offerData.lender.logoUrl} alt={`${offerData.lender.lenderName} Logo`} />) : (<div className="h-12 w-12 bg-gray-200 rounded-md flex items-center justify-center text-gray-500 text-xs flex-shrink-0">No Logo</div>)}<div><p className="text-lg font-semibold text-gray-900">{offerData.lender?.lenderName || 'Lender'}</p><p className="text-sm text-gray-500">{offerData.lender?.lenderType || 'Financial Institution'}</p></div></div><div className="grid grid-cols-2 gap-x-6 gap-y-4"><div><p className="text-sm text-gray-500 mb-0.5">Credit Limit</p><p className="text-md font-semibold text-gray-800">{formatCurrency(offerData.creditLimit)}</p></div><div><p className="text-sm text-gray-500 mb-0.5">Service Fee %</p><p className="text-md font-semibold text-gray-800">{offerData.interestRate}%</p></div><div><p className="text-sm text-gray-500 mb-0.5">Tenure</p><p className="text-md font-semibold text-gray-800">{offerData.tenureDays} days</p></div><div><p className="text-sm text-gray-500 mb-0.5">Processing Fee</p><p className="text-md font-semibold text-gray-800">{offerData.processingFee.type === 'percentage' ? `${offerData.processingFee.value}%` : formatCurrency(offerData.processingFee.value)}</p></div></div><p className="text-sm text-gray-600 pt-3">By clicking "Accept Offer", you confirm agreement to these terms.</p></div><div className="flex justify-end items-center px-6 py-4 bg-gray-50 border-t border-gray-200 space-x-3"><button onClick={onClose} type="button" className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${isAccepting ? 'bg-gray-200 text-gray-400 cursor-not-allowed' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'}`} disabled={isAccepting}>Cancel</button><button onClick={onConfirm} type="button" className={`px-5 py-2 rounded-md text-sm font-medium text-white transition-colors flex items-center justify-center ${isAccepting ? 'bg-green-400 cursor-not-allowed' : 'bg-green-600 hover:bg-green-700'}`} disabled={isAccepting}>{isAccepting ? (<><SpinnerIcon /><span className="ml-2">Processing...</span></>) : 'Accept Offer'}</button></div></div></div>);
};


export default ActivationJourney;